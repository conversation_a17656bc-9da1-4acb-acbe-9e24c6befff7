const Device = require('../models/device');
const ADMIN_PHONE_NUMBER = process.env.ADMIN_PHONE_NUMBER;
const otpGenerator = require('otp-generator');
// Load input validation
const { sendOtp } = require("../utils/channel");
// Load User model
const User = require("../models/user");
const Wallet = require("../models/wallet");
 
const otps = [];
const ObjectId = require('mongoose').Types.ObjectId;

const requestWithdraw = async(req, res)=>{
    try {
        const { payAmount } = req.body;
        await Wallet.updateOne({ user: `${req.user._id}` }, {
            $set: { user: `${req.user._id}` },
            $inc: { currentBalance: parseInt(-payAmount) },
            $push: { requests: { ts: new Date(), _id: new ObjectId(), amount: parseInt(payAmount), currentBalance: req.user?.balance || 0, status: 'pending' } },
        }, { upsert: true });
        await User.updateOne({ _id: req.user._id }, { $inc: { balance: parseInt(-payAmount) } });
        res.json({ success: true });

    }
    catch (err) {
        res.json({ success: false, err });
    }
}

const setPincode = async (req, res) => {
    const { phoneNumber, username, oldPinCode, newPinCode } = req.body;

    User.findOne({ phoneNumber }).then(async (user) => {
        try {

            if (user.pinCode != oldPinCode) {
                return res.status(200).json({ success: false, message: "Mistake old pin code!" })
            } else {
                user.updateOne({ pinCode: newPinCode, username })
                    .then(
                        user => {
                            // console.log(user);
                            return res.status(200).json({ success: true, message: 'Pin code & username set successfully' })
                        }
                    )
            }
        } catch (e) {
            console.log(e);
        };
    });
}

const setPin = async (req, res) => {
    const { phoneNumber, newPinCode } = req.body;
  
    try {
      // 1) Find the user by phoneNumber
      const user = await User.findOne({ phoneNumber });
      if (!user) {
        return res.status(200).json({
          success: false,
          message: 'User not found',
        });
      }
  
      // 2) Update the user's pinCode
      user.pinCode = newPinCode;
      await user.save();
  
      // 3) Send success
      return res.status(200).json({
        success: true,
        message: 'Pin code set successfully!',
      });
    } catch (error) {
      console.error(error);
      // If something goes wrong
      return res.status(500).json({
        success: false,
        message: 'Error setting pin code.',
      });
    }
  };
  

const updateBank = async(req, res)=>{
    try {
        const { bankName, bankAccount } = req.body;
        await Wallet.updateOne({ user: `${req.user._id}` }, {
            $set: { bankName, bankAccount, user: `${req.user._id}` }
        }, { upsert: true });
        res.json({ success: true });

    }
    catch (err) {
        res.json({ success: false, err });
    }
}

const resetPassword = async(req, res) => {
    const { phoneNumber, pinCode, deviceNumber } = req.body; // deviceNumber will now be used instead of otp
    User.findOne({ phoneNumber }).then(async (user) => {
        if (user) {
            // Find user's devices and get the default device
            const devices = await Device.find({ phoneNumber: user.phoneNumber });
            const defaultDevice = devices.find((device) => device.isDefault);

            if (!defaultDevice) {
                return res.status(400).json({ success: false, message: "No default device found" });
            }

            // Extract the last 6 digits of the default device number
            const deviceNumberLast6Digits = defaultDevice.deviceNumber.slice(-6);

            if (deviceNumber === deviceNumberLast6Digits) {
                // If the provided device number matches the last 6 digits of the default device's device number
                user.pinCode = pinCode;  // Reset the pin code
                await user.save(); // Save the updated user document

                return res.status(200).json({ message: "Password reset successfully", success: true });
            } else {
                return res.status(400).json({ success: false, message: "Incorrect device number" });
            }
        } else {
            return res.status(404).json({ success: false, message: "User not found with this phone number" });
        }
    });
};

const myAccount = async(req,res)=>{
    try {
        let remainDays = 0;
        const user = req.user;
        const devices = await Device.find({ phoneNumber: user.phoneNumber });
        const defaultDevices = devices.filter((d) => d.isDefault);
        const wallet = await Wallet.findOne({ user: `${user._id}` });

        let status = user.status;
        if (user.phoneNumber != ADMIN_PHONE_NUMBER) {

            if (user.expired) {
                const offset = new Date(user.expired).getTime() - Date.now();

                if (offset < 0) {
                    status = "expired";
                } else {
                    if (user.licenseKey == undefined || user.licenseKey == "") {
                        status = "trial";
                    }
                    remainDays = offset;

                }

            } else {
                status = "expired"
            }
        }

        res.status(200).json({
            user: {
                phoneNumber: user.phoneNumber,
                role: user.role,
                pinCode: user.pinCode,
                status: status,
                device: ((defaultDevices != null && defaultDevices.length > 0 && defaultDevices[0].deviceNumber != "") ? defaultDevices[0] : null),
                devices,
                licenseKey: user.licenseKey,
                expired: user.expired,
                username: user.username,
                remainDays: remainDays, _id: user._id,
                driverLicenseFile: user?.driverLicenseFile || '',
                driverLicenseVerification: user?.driverLicenseVerification || 0,
                address: user?.address || '',
                description: user?.description || '',
                balance: user?.balance || 0,
                wallet
            }
        });
    } catch (error) {
        res.status(200).json({
            success: false,
            error: 'Your request could not be processed. Please try again.'
        });
    }
}

const savefcmtoken = async(req, res)=>{
    try {
        const { userid, fcmToken } = req.body;

        // Find the user document using the userid
        const user = await User.findById(userid);

        if (!user) {
            res.status(404).json({ message: 'User not found' });
            return;
        }

        // Update the fmctoken field of all the devices owned by the user
        await Device.updateMany({ phoneNumber: user.phoneNumber }, { fmctoken: fcmToken });

        res.status(200).json({ message: 'FCM token saved to all devices owned by the user' });
    }
    catch (err) {
        res.json({ success: false, err });
    }
}

const updateUserExpiry = async (req, res) => {
    try {
        const { days } = req.body;
        const user = req.user;

        // Calculate new expiry date
        let newExpiry;
        const currentDate = new Date();
        const currentExpiry = new Date(user.expired);

        // If current expiry is in the future, add days to it
        // Otherwise, add days to current date
        if (currentExpiry > currentDate) {
            newExpiry = new Date(currentExpiry.getTime() + days * 24 * 60 * 60 * 1000);
        } else {
            newExpiry = new Date(currentDate.getTime() + days * 24 * 60 * 60 * 1000);
        }

        // Update user expiry
        await User.updateOne(
            { _id: user._id },
            {
                $set: {
                    expired: newExpiry,
                    status: 'active'
                }
            }
        );

        // Update corresponding license if exists
        await License.updateOne(
            { owner: user._id },
            {
                $set: {
                    expired: newExpiry
                }
            }
        );

        // Find user's devices and get the default device
        const devices = await Device.find({ phoneNumber: user.phoneNumber });
        const defaultDevice = devices.find((device) => device.isDefault);

        // Send MQTT message if device exists
        if (defaultDevice && defaultDevice.deviceNumber) {
            const mqttPayload = {
                id: defaultDevice.deviceNumber,
                command: 'license'
            };

            // Publish the MQTT message
            mqttClient.schedpublish({
                topic: defaultDevice.deviceNumber,
                payload: JSON.stringify(mqttPayload),
                qos: 0,
                retain: false
            }, false);
        }

        return res.json({ 
            success: true,
            message: "Expiry date updated successfully",
            newExpiry: newExpiry
        });
    } catch (err) {
        console.error('Error updating expiry:', err);
        return res.json({
            success: false,
            error: err.message
        });
    }
};

module.exports = {
    requestWithdraw, updateBank, resetPassword, myAccount,setPincode, savefcmtoken, setPin, updateUserExpiry
}
