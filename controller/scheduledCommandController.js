const ScheduledCommand = require('../models/scheduledCommand');
const Device = require('../models/device');
const mqttClient = require('../utils/mqtt');
const nodeCron = require('node-cron');

// Store active cron jobs with their IDs
const activeJobs = {};

// Store device responses from check commands
const deviceResponses = {};

// Create a new scheduled command
const createScheduledCommand = async (req, res) => {
  try {
    const { deviceNumber, topic, command, scheduledTime, isRecurring } = req.body;

    // Validate required fields
    if (!deviceNumber || !command || !scheduledTime) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields'
      });
    }

    // Verify device belongs to user
    const device = await Device.findOne({ 
      deviceNumber, 
      phoneNumber: req.user.phoneNumber 
    });

    if (!device) {
      return res.status(404).json({
        success: false,
        message: 'Device not found or not authorized'
      });
    }

    // Format the payload correctly
    const payload = JSON.stringify({
      id: deviceNumber,
      command: command
    });

    // Create scheduled command
    const scheduledCommand = new ScheduledCommand({
      user: req.user._id,
      deviceNumber,
      topic: topic || deviceNumber, // Default to deviceNumber if topic not provided
      payload,
      scheduledTime: new Date(scheduledTime),
      isRecurring
    });

    await scheduledCommand.save();

    // Schedule the command
    scheduleCommand(scheduledCommand);

    return res.status(201).json({
      success: true,
      message: 'Command scheduled successfully',
      scheduleId: scheduledCommand._id
    });
  } catch (error) {
    console.error('Error creating scheduled command:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// Get all scheduled commands for a user
const getScheduledCommands = async (req, res) => {
  try {
    const scheduledCommands = await ScheduledCommand.find({ user: req.user._id });
    
    return res.json({
      success: true,
      data: scheduledCommands
    });
  } catch (error) {
    console.error('Error fetching scheduled commands:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// Get a specific scheduled command
const getScheduledCommandById = async (req, res) => {
  try {
    const scheduledCommand = await ScheduledCommand.findOne({
      _id: req.params.id,
      user: req.user._id
    });
    
    if (!scheduledCommand) {
      return res.status(404).json({
        success: false,
        message: 'Scheduled command not found'
      });
    }
    
    return res.json({
      success: true,
      data: scheduledCommand
    });
  } catch (error) {
    console.error('Error fetching scheduled command:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// Update a scheduled command
const updateScheduledCommand = async (req, res) => {
  try {
    const { topic, payload, scheduledTime, isRecurring } = req.body;
    
    const scheduledCommand = await ScheduledCommand.findOne({
      _id: req.params.id,
      user: req.user._id
    });
    
    if (!scheduledCommand) {
      return res.status(404).json({
        success: false,
        message: 'Scheduled command not found'
      });
    }
    
    // Update fields if provided
    if (topic) scheduledCommand.topic = topic;
    if (payload) scheduledCommand.payload = payload;
    if (scheduledTime) scheduledCommand.scheduledTime = new Date(scheduledTime);
    if (isRecurring !== undefined) scheduledCommand.isRecurring = isRecurring;
    
    // Reset status if time is changed
    if (scheduledTime) {
      scheduledCommand.status = 'pending';
      scheduledCommand.lastExecuted = null;
    }
    
    await scheduledCommand.save();
    
    // Reschedule the command
    if (activeJobs[scheduledCommand._id]) {
      activeJobs[scheduledCommand._id].stop();
      delete activeJobs[scheduledCommand._id];
    }
    scheduleCommand(scheduledCommand);
    
    return res.json({
      success: true,
      message: 'Scheduled command updated successfully',
      data: scheduledCommand
    });
  } catch (error) {
    console.error('Error updating scheduled command:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// Delete a scheduled command
const deleteScheduledCommand = async (req, res) => {
  try {
    const scheduledCommand = await ScheduledCommand.findOne({
      _id: req.params.id,
      user: req.user._id
    });
    
    if (!scheduledCommand) {
      return res.status(404).json({
        success: false,
        message: 'Scheduled command not found'
      });
    }
    
    // Stop the cron job if it exists
    if (activeJobs[scheduledCommand._id]) {
      activeJobs[scheduledCommand._id].stop();
      delete activeJobs[scheduledCommand._id];
    }
    
    await ScheduledCommand.deleteOne({ _id: scheduledCommand._id });
    
    return res.json({
      success: true,
      message: 'Scheduled command deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting scheduled command:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};

// Helper function to schedule a command
const scheduleCommand = async (command) => {
  const commandId = command._id.toString();
  
  // If it's a one-time command in the past, mark as failed
  if (!command.isRecurring && new Date(command.scheduledTime) < new Date()) {
    await ScheduledCommand.findByIdAndUpdate(commandId, {
      status: 'failed',
      lastExecuted: new Date()
    });
    console.log(`Command ${commandId} scheduled time is in the past`);
    return;
  }
  
  if (command.isRecurring) {
    // For recurring commands, extract hour and minute for daily scheduling
    const scheduledDate = new Date(command.scheduledTime);
    const minute = scheduledDate.getMinutes();
    const hour = scheduledDate.getHours();
    
    // Schedule daily at the specified time
    const cronExpression = `${minute} ${hour} * * *`;
    
    activeJobs[commandId] = nodeCron.schedule(cronExpression, async () => {
      await executeCommand(command);
    });
    
    console.log(`Recurring command ${commandId} scheduled for ${hour}:${minute} daily`);
  } else {
    // For one-time commands, calculate delay in milliseconds
    const now = new Date();
    const scheduledTime = new Date(command.scheduledTime);
    const delay = scheduledTime.getTime() - now.getTime();
    
    if (delay > 0) {
      // Schedule the one-time command
      const timeoutId = setTimeout(async () => {
        await executeCommand(command);
        delete activeJobs[commandId];
      }, delay);
      
      activeJobs[commandId] = {
        stop: () => clearTimeout(timeoutId)
      };
      
      console.log(`One-time command ${commandId} scheduled for ${scheduledTime.toISOString()}`);
    }
  }
};

// Helper function to execute a command with pre-check
const executeCommand = async (command) => {
  try {
    const commandId = command._id.toString();
    const deviceNumber = command.deviceNumber;
    console.log(`Preparing to execute scheduled command ${commandId} for device ${deviceNumber}`);
    
    // Parse the payload to get the command type
    let payload;
    try {
      payload = JSON.parse(command.payload);
    } catch (e) {
      console.error(`Error parsing payload for command ${commandId}:`, e);
      payload = command.payload;
    }
    
    const commandType = typeof payload === 'object' ? payload.command : payload;
    
    // Step 1: Send a check command to verify device status
    console.log(`Sending check command to device ${deviceNumber} before executing ${commandType}`);
    
    // Import MQTT client
    const mqttClient = require('../utils/mqtt');
    
    // Check if MQTT client is available and connected
    if (!mqttClient || !mqttClient.client || !mqttClient.client.connected) {
      console.error('MQTT client not available or not connected');
      
      // Update command status to failed
      await ScheduledCommand.findByIdAndUpdate(commandId, {
        status: 'failed',
        lastExecuted: new Date(),
        executionResult: 'MQTT client not connected'
      });
      
      return 'failed';
    }
    
    // Send the check command with response handling
    const checkMessage = {
      topic: deviceNumber,
      payload: JSON.stringify({
        id: deviceNumber,
        command: "check"
      }),
      qos: 0
    };
    
    // Use the new schedpublishWithResponse function
    const checkResult = await mqttClient.schedpublishWithResponse(checkMessage, 10000);
    
    // Step 3: Analyze device response
    let shouldExecute = true;
    let skipReason = null;
    
    if (!checkResult.success) {
      console.log(`Device ${deviceNumber} is offline, marking command as failed`);
      shouldExecute = false;
      skipReason = "Device offline";
    } else {
      console.log(`Analyzing response from device ${deviceNumber}:`, checkResult.response);
      
      // Check voltage level if available
      if (checkResult.response.volt !== undefined) {
        const voltage = parseFloat(checkResult.response.volt);
        const isRunning = voltage > 13.5;
        
        console.log(`Device ${deviceNumber} voltage: ${voltage}V, running status: ${isRunning}`);
        
        // Skip turn on command if already running
        if (commandType === "as" && isRunning) {
          console.log(`Skipping turn on command for device ${deviceNumber} - already running`);
          shouldExecute = false;
          skipReason = "Device already running";
        }
        
        // Skip turn off command if already off
        if (commandType === "untar" && !isRunning) {
          console.log(`Skipping turn off command for device ${deviceNumber} - already off`);
          shouldExecute = false;
          skipReason = "Device already off";
        }
      }
    }
    
    // Step 4: Execute the command if appropriate
    let executionStatus = "skipped";
    let executionResult = skipReason;
    
    if (shouldExecute) {
      console.log(`Executing scheduled command ${commandId} (${commandType}) for device ${deviceNumber}`);
      
      // Prepare MQTT message
      const mqttMessage = {
        topic: command.topic,
        payload: typeof payload === 'object' ? JSON.stringify(payload) : payload,
        qos: 0
      };
      
      try {
        // Send MQTT message with response handling
        const result = await mqttClient.schedpublishWithResponse(mqttMessage, 10000);
        executionStatus = result.success ? 'executed' : 'failed';
        executionResult = result.success ? JSON.stringify(result.response) : result.error;
        
        console.log(`Command ${commandId} execution ${executionStatus}`);
      } catch (error) {
        console.error(`Error executing command ${commandId}:`, error);
        executionStatus = 'failed';
        executionResult = error.message;
      }
    } else {
      console.log(`Command ${commandId} execution skipped: ${skipReason}`);
    }
    
    // Step 5: Update command status
    await ScheduledCommand.findByIdAndUpdate(commandId, {
      status: executionStatus,
      lastExecuted: new Date(),
      executionResult: executionResult
    });
    
    // Log the complete execution process
    console.log(`Command ${commandId} completed with status: ${executionStatus}`);
    
    return executionStatus;
  } catch (error) {
    console.error(`Error in command execution process:`, error);
    
    // Update command status to failed
    await ScheduledCommand.findByIdAndUpdate(command._id, {
      status: 'failed',
      lastExecuted: new Date(),
      executionResult: error.message
    });
    
    return 'failed';
  }
};

// Initialize all scheduled commands on server start
const initializeScheduledCommands = async () => {
  try {
    const pendingCommands = await ScheduledCommand.find({
      $or: [
        { status: 'pending' },
        { isRecurring: true }
      ]
    });
    
    console.log(`Initializing ${pendingCommands.length} scheduled commands`);
    
    pendingCommands.forEach(command => {
      scheduleCommand(command);
    });
  } catch (error) {
    console.error('Error initializing scheduled commands:', error);
  }
};

module.exports = {
  createScheduledCommand,
  getScheduledCommands,
  getScheduledCommandById,
  updateScheduledCommand,
  deleteScheduledCommand,
  initializeScheduledCommands
};
