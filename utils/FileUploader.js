const multer = require('multer');
const path = require('path');

const imageStorage = multer.diskStorage({
    destination: './uploads/images',
    filename: (req, file, cb) => {
        cb(
            null,
            file.fieldname + "-c" + Date.now() + path.extname(file.originalname)
        )
    }
})

const imageUpload = multer({
    storage: imageStorage,
    limits: {
        fileSize: 1024 * 1024 * 10
    },
    fileFilter: (req, file, cb) => {
        if (!file.originalname.toLowerCase().match(/\.(png|jpg|jpeg|bmp|tif|tiff|webp)$/)) {
            // upload only png and jpg format

            return cb(new Error("Please upload a Image with image types"));
        }
        cb(null, true);
    }
})

module.exports = {
    imageUpload
}