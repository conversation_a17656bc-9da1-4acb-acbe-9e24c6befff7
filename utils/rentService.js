let rentableCars = [];
let rentableMopeds =[];


const getRentableCarList = () => {
    return rentableCars;
}

const updateRentableCarList = (cars) => {
    rentableCars = cars;
}

const getRentableMopedList = () => {
    return rentableMopeds;
}

const updateRentableMopedList = (mopeds) => {
    rentableMopeds = mopeds;
}
const getCarRentPrice = (mode) => {
    const table = getCarRentPriceTable();
    return (mode == 'minute' ? table[0] : (mode == 'hour' ? table[1] : table[2]));
}
const getMopedRentPrice = (mode) => {
    const table = getMopedRentPriceTable();
    return (mode == 'minute' ? table[0] : (mode == 'hour' ? table[1] : table[2]));
}
const getBikeRentPrice = (mode) => {
    const table = getBikeRentPriceTable(); // Use a separate table for bikes.
    return (mode == 'minute' ? table[0] : (mode == 'hour' ? table[1] : table[2]));
}

const getCarRentPriceTable = () => {
    const pricePerMinute = parseInt(process.env.CAR_RENT_MINUTE);
    const pricePerHour = parseInt(process.env.CAR_RENT_HOURLY);
    const pricePerDay = parseInt(process.env.CAR_RENT_DAILY);
    return [pricePerMinute, pricePerHour, pricePerDay]
}

const getMopedRentPriceTable = () => {
    const pricePerMinute = parseInt(process.env.MOPED_RENT_MINUTE);
    const pricePerHour = parseInt(process.env.MOPED_RENT_HOURLY);
    const pricePerDay = parseInt(process.env.MOPED_RENT_DAILY);
    return [pricePerMinute, pricePerHour, pricePerDay]
}
module.exports = {
    getRentableCarList,
    updateRentableCarList,
    getCarRentPrice,
    getCarRentPriceTable,
    getRentableMopedList,
    updateRentableMopedList,
    getMopedRentPrice,
    getMopedRentPriceTable
}
