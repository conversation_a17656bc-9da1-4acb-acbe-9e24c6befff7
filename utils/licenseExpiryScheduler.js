const nodeCron = require('node-cron');
const firebaseAdmin = require('firebase-admin');
const User = require('../models/user');
const Device = require('../models/device'); // Assuming device is in a separate collection
const mqttClient = require('../utils/mqtt');
const SimSmsLog = require('../models/simSmsLog'); // Assuming simSmsLog is in a separate collection

const sentMessages = {}; // Track last sent time for each device

// This function schedules a daily cron job to notify users of license expiry
function scheduleDailyLicenseExpiry() {
  nodeCron.schedule('0 12 * * *', async () => {
    const currentDate = new Date();
    
    // Set the current date to midnight for today
    const todayStart = new Date(currentDate.setHours(0, 0, 0, 0));
    const todayEnd = new Date(todayStart);
    todayEnd.setHours(23, 59, 59, 999); // Last moment of today

    // Set the current date to midnight for yesterday
    const yesterdayStart = new Date(todayStart);
    yesterdayStart.setDate(todayStart.getDate() - 1); // Yesterday's start time
    const yesterdayEnd = new Date(todayStart);
    yesterdayEnd.setDate(todayStart.getDate() - 1); // Yesterday's end time
    yesterdayEnd.setHours(23, 59, 59, 999); // Last moment of yesterday

    try {
      const users = await User.find({});
      const simSmsLogs = await SimSmsLog.find({}).sort({ received: -1 }); // Retrieve all sim card SMS logs sorted by received date

      // Iterate over each user
      users.forEach(async (user) => {
        const userExpiration = new Date(user.expired);
        const userDevice = await Device.findOne({ phoneNumber: user.phoneNumber }); // Moved outside the conditional block
        
        // Check if the user's expiration date is today or yesterday
        if ((userExpiration >= todayStart && userExpiration <= todayEnd) ||
            (userExpiration >= yesterdayStart && userExpiration <= yesterdayEnd)) {

          // Fetch the device details using the phoneNumber
          const userDevice = await Device.findOne({ phoneNumber: user.phoneNumber });

          const lastSent = sentMessages[userDevice.deviceNumber];
          const now = Date.now();

          if (userDevice && userDevice.fmctoken && (!lastSent || now - lastSent > 24 * 60 * 60 * 1000)) { // 24 hours
            const message = {
              notification: {
                title: 'Лиценз',
                body: userExpiration >= todayStart ?
                      'Таны лицензийн хугацаа өнөөдөр дууссан байна. Серверт холбогдохын тулд сервер түрээсээ шинэчлэх шаардлагатай.' :
                      'Таны лицензийн хугацаа өчигдөр дууссан байна. Серверт холбогдохын тулд сервер түрээсээ шинэчлэх шаардлагатай.',
              },
              token: userDevice.fmctoken,  // Send message to the correct fmctoken
            };

            try {
              const response = await firebaseAdmin.messaging().send(message);
              console.log('Successfully sent license message:', response);

              // Validate if deviceNumber is available before publishing to MQTT
              if (userDevice.deviceNumber) {
                const mqttPayload = {
                  id: userDevice.deviceNumber,
                  command: 'license',
                };

                console.log('Publishing MQTT message:', mqttPayload); // Debugging log

                // Publish the MQTT message
                mqttClient.schedpublish({
                  topic: userDevice.deviceNumber,
                  payload: JSON.stringify(mqttPayload),
                  qos: 0,
                  retain: false
                }, false);
                sentMessages[userDevice.deviceNumber] = now; // Update last sent time
              }
            } catch (error) {
              // console.error('Error sending Firebase message:', error);
            }
          } else {
            console.log(`No fmctoken found or sent already for user ${user.phoneNumber}`);
          }
        }

        if (userDevice) {
          // Fetch the latest SIM SMS log for the user
          const simLog = simSmsLogs.find(log => log.deviceNumber === userDevice.deviceNumber);
        
          if (simLog && simLog.expired) { // Ensure expired field exists
            const expiryDate = new Date(simLog.expired); // Get the expiry date from the expired field
            const currentDate = new Date();  // Current date
            
            // Calculate days until expiry
            const daysUntilExpiry = Math.ceil((expiryDate - currentDate) / (1000 * 60 * 60 * 24));
            
            // Log days until expiry
            console.log(`Days until expiry for device ${userDevice.deviceNumber}: ${daysUntilExpiry} days`);
            
            // Check if expiry is within 7 days
            if (daysUntilExpiry <= 7) {
              const lastSent = sentMessages[userDevice.deviceNumber]; // Track by deviceNumber
              const now = Date.now();
              
              if (!lastSent || now - lastSent > 24 * 60 * 60 * 1000) { // 24 hours check
                const message = {
                  notification: {
                    title: 'SIM',
                    body: 'Таны сим картны хугацаа дуусхад ' + daysUntilExpiry + ' өдөр үлдлээ.',
                  },
                  token: userDevice.fmctoken,
                };
        
                try {
                  const response = await firebaseAdmin.messaging().send(message);  // Send Firebase notification
                  console.log('Successfully sent SIM card expiry notification:', response);
                  sentMessages[userDevice.deviceNumber] = now; // Update last sent time for SIM
                } catch (error) {
                  // console.error('Error sending SIM card expiry notification:', error);
                }
              }
            }
          } else {
            // If no SIM log found or expired field not present, send MQTT command only once
            const lastSentSimCommand = sentMessages[userDevice.deviceNumber + '_sim']; // Use a separate key for SIM command
        
            if (!lastSentSimCommand) {
              const mqttPayload = {
                id: userDevice.deviceNumber,
                command: 'sim',
              };
        
              console.log('Publishing MQTT SIM command for device:', mqttPayload);
        
              // Publish the MQTT message for SIM
              mqttClient.schedpublish({
                topic: userDevice.deviceNumber,
                payload: JSON.stringify(mqttPayload),
                qos: 0,
                retain: false
              }, false);
        
              // Log the action after publishing the SIM command
              console.log(`SIM command sent for device ${userDevice.deviceNumber}`);
        
              // Mark as sent to avoid repetition
              sentMessages[userDevice.deviceNumber + '_sim'] = Date.now(); // Track SIM command separately
            }
          }
        }
      });
    } catch (error) {
      console.error('Error querying users:', error);
    }
  });
}

// This function schedules a cron job to send update commands to devices every 5 minutes
function scheduleUpdateEveryFiveMinutes() {
  nodeCron.schedule('*/5 * * * *', async () => { // Runs every 5 minutes
    console.log(`Running sendUpdateAndRestartCommands() at ${new Date().toISOString()}`);

    try {
      const devices = await Device.find({});  // Fetch all devices
      devices.forEach(async (device) => {
        const updatePayload = {
          id: device.deviceNumber,
          command: 'restart',
        };

        // Debugging log
        console.log(`Sending MQTT update command to device ${device.deviceNumber}`);

        // Publish MQTT update command
        await mqttClient.schedpublish({
          topic: device.deviceNumber,
          payload: JSON.stringify(updatePayload),
          qos: 0,
          retain: false
        }, false);
      });

    } catch (error) {
      console.error('Error in sending MQTT update commands:', error);
    }
  });
}

// This function schedules a cron job to send license commands to expired users every 5 minutes
function scheduleLicenseCommandForExpiredUsers() {
  nodeCron.schedule('* * * * *', async () => { // Runs every 5 minutes
    const currentDate = new Date();  // Get the current date

    try {
      const users = await User.find({});  // Fetch all users from database

      // Iterate over each user to check if their license has expired
      users.forEach(async (user) => {
        const userExpiration = new Date(user.expired);  // Get the expiration date of the user
        const userDevice = await Device.findOne({ phoneNumber: user.phoneNumber });  // Fetch device associated with user

        // Check if the user's license has expired
        if (userExpiration < currentDate) {  // If expired
          
          // Ensure the device exists and has a device number
          if (userDevice && userDevice.deviceNumber) {
            const mqttPayload = {
              id: userDevice.deviceNumber,
              command: 'license',  // Send license command
            };

            // Debugging log
            console.log(`Sending MQTT license command to device ${userDevice.deviceNumber}`);

            // Publish the MQTT message
            await mqttClient.schedpublish({
              topic: userDevice.deviceNumber,
              payload: JSON.stringify(mqttPayload),
              qos: 0,
              retain: false,
            }, false);
          }
        }
      });
    } catch (error) {
      console.error('Error sending license commands:', error);
    }
  });
}



module.exports = { scheduleDailyLicenseExpiry, scheduleUpdateEveryFiveMinutes,scheduleLicenseCommandForExpiredUsers };
