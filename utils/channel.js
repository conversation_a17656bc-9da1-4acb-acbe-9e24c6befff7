const axios = require('axios');


const sendSms = async(data, options, res) => {

    try {
        // const url = `${process.env.SMS_SERVICE_URL}?uname=${process.env.SMS_USER_NAME}&upass=${process.env.SMS_USER_PWD}&from=${data.from}&mobile=${data.mobile}&sms=sendsms`;
        const SMS_ADMIN_PHONE_NUMBER = 132933;
        const mobile = data.mobile;

        let url = `${process.env.SMS_SERVICE_URL}?uname=${process.env.SMS_USER_NAME}&upass=${process.env.SMS_USER_PWD}&from=${SMS_ADMIN_PHONE_NUMBER}&mobile=${data.mobile}&sms=${data.sms}`;
        // const url = "http://sms.unitel.mn/sendSMS.php?uname=elec&upass=Unitel88&sms=as&from=132933&mobile=89932933";

        // send command to iot device
        if (`${mobile}`.startsWith('9') || `${mobile}`.startsWith("85")) {

            url = `${process.env.SMS_SERVICE_URL_N9}/mt?servicename=elec&username=service&from=${SMS_ADMIN_PHONE_NUMBER}&to=${mobile}&msg=${data.sms}`;
            //    console.log(url, "is sms url");
        }
        const response = await axios.get(url, {});
        console.log(data, "is send to sms");
        return response;
    } catch (err) {
        console.log(err, "is error");
        return null;
    }

}
const sendOtp = async(phoneNumber, otp) => {
    try {
        console.log(otp)

        const SMS = `
        Sain bn u, Tani otp kod ${otp} Aslaa.mn
        `;
        const SMS_ADMIN_PHONE_NUMBER = 132933;
        let url = `
            ${process.env.SMS_SERVICE_URL}?uname=${process.env.SMS_USER_NAME}&upass=${process.env.SMS_USER_PWD}&from=${SMS_ADMIN_PHONE_NUMBER}&mobile=${phoneNumber}&sms=${SMS}
            `;
        if (`${phoneNumber}`.startsWith("9") || `${phoneNumber}`.startsWith("85")) {
            url = `${process.env.SMS_SERVICE_URL_N9}/mt?servicename=elec&username=service&from=${SMS_ADMIN_PHONE_NUMBER}&to=${phoneNumber}&msg=${SMS}`;
            console.log(url, "is opt mobi url");
        }
        const response = await axios.get(url, {});

        return response;
    } catch (err) {
        return null;
    }

}


const sendMqtt = async(data, options, res) => {
    try {
        const response = await axios.post(process.env.MQTT_SERVICE_URL, data, options);


        return response;
    } catch (err) {
        return null;
    }

}

const publish = async (req, res) => {
    try {
        const data = req.body; // Get the request data

        // Set up the request options with headers
        const options = {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Basic ${Buffer.from(`${process.env.MQTT_USER_NAME}:${process.env.MQTT_USER_PWD}`).toString('base64')}`
            }
        };

        // Make the post request to the MQTT service
        const response = await axios.post(process.env.MQTT_SERVICE_URL, data, options);

        // Return the response to the client
        return res.status(response.status).json(response.data);
    } catch (err) {
        console.error('Error publishing message:', err);
        // Handle the error and return a proper message to the client
        return res.status(500).json({ error: 'Failed to publish message', details: err.message });
    }
};


const getMqttClients = async(options) => {
    try {
        // console.log(`${process.env.MQTT_SERVICE_CHECK_URL}/${clientId }`, options);
        const response = await axios.get(`${process.env.MQTT_SERVICE_CHECK_URL}?_page=1&_limit=1000`, options);

        if (response.status == 200 && response.data && response.data.data) {
            return { success: true, clients: response.data.data.filter((c) => c.username == 'admin') }
        } else
            return { success: false, clients: [] }

    } catch (err) {
        return { success: false, clients: [], err }

    }
}
const checkMqttClient = async(clientId, options) => {
    try {
        // console.log(`${process.env.MQTT_SERVICE_CHECK_URL}/${clientId }`, options);
        const response = await axios.get(`${process.env.MQTT_SERVICE_CHECK_URL}/${clientId}`, options);
        // Check if response and response.data exist and have the expected structure
        if (response && response.data && response.data.data && response.data.data.length > 0) {
            return response.data.data[0].connected;
        } else {
            console.log(`Client ${clientId} not found or has unexpected response structure`);
            return false;
        }
    } catch (err) {
        console.log(`Error checking MQTT client ${clientId}: ${err.message}`);
        return false;
    }
}


module.exports = { sendSms, sendMqtt, sendOtp, checkMqttClient, getMqttClients,publish }
