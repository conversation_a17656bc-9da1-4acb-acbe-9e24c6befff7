{"version": 3, "sources": ["../node_modules/@mui/base/utils/resolveComponentProps.js", "../node_modules/@mui/base/utils/appendOwnerState.js", "../node_modules/@mui/material/Fade/Fade.js", "../node_modules/@mui/utils/esm/getScrollbarSize.js", "../node_modules/@mui/utils/esm/createChainedFunction.js", "../node_modules/@mui/base/Portal/Portal.js", "../node_modules/@mui/base/FocusTrap/FocusTrap.js", "../node_modules/@mui/base/ModalUnstyled/modalUnstyledClasses.js", "../node_modules/@mui/base/ModalUnstyled/ModalManager.js", "../node_modules/@mui/base/ModalUnstyled/ModalUnstyled.js", "../node_modules/@mui/material/Modal/Modal.js", "../node_modules/@mui/base/utils/omitEventHandlers.js", "../node_modules/@mui/base/utils/mergeSlotProps.js", "../node_modules/@mui/base/utils/extractEventHandlers.js", "../node_modules/@mui/base/utils/useSlotProps.js", "../node_modules/@mui/material/styles/getOverlayAlpha.js", "../node_modules/@mui/material/Paper/paperClasses.js", "../node_modules/@mui/material/Paper/Paper.js", "../node_modules/@mui/material/Backdrop/backdropClasses.js", "../node_modules/@mui/material/Backdrop/Backdrop.js"], "names": ["resolveComponentProps", "componentProps", "ownerState", "appendOwnerState", "elementType", "otherProps", "undefined", "isHostComponent", "_extends", "_excluded", "styles", "entering", "opacity", "entered", "Fade", "React", "props", "ref", "theme", "useTheme", "defaultTimeout", "enter", "transitions", "duration", "enteringScreen", "exit", "leavingScreen", "addEndListener", "appear", "children", "easing", "in", "inProp", "onEnter", "onEntered", "onEntering", "onExit", "onExited", "onExiting", "style", "timeout", "TransitionComponent", "Transition", "other", "_objectWithoutPropertiesLoose", "nodeRef", "handleRef", "useForkRef", "normalizedTransitionCallback", "callback", "maybeIsAppearing", "node", "current", "handleEntering", "handleEnter", "isAppearing", "reflow", "transitionProps", "getTransitionProps", "mode", "webkitTransition", "create", "transition", "handleEntered", "handleExiting", "handleExit", "handleExited", "_jsx", "next", "state", "childProps", "visibility", "getScrollbarSize", "doc", "documentWidth", "documentElement", "clientWidth", "Math", "abs", "window", "innerWidth", "createChainedFunction", "_len", "arguments", "length", "funcs", "Array", "_key", "reduce", "acc", "func", "_len2", "args", "_key2", "apply", "this", "Portal", "container", "disable<PERSON><PERSON><PERSON>", "mountNode", "setMountNode", "useEnhancedEffect", "getContainer", "document", "body", "setRef", "newProps", "ReactDOM", "candidatesSelector", "join", "defaultGetTabbable", "root", "regularTabNodes", "orderedTabNodes", "from", "querySelectorAll", "for<PERSON>ach", "i", "nodeTabIndex", "tabindexAttr", "parseInt", "getAttribute", "Number", "isNaN", "contentEditable", "nodeName", "tabIndex", "getTabIndex", "disabled", "tagName", "type", "name", "getRadio", "selector", "ownerDocument", "querySelector", "concat", "roving", "isNonTabbableRadio", "isNodeMatchingSelectorFocusable", "push", "documentOrder", "sort", "a", "b", "map", "defaultIsEnabled", "FocusTrap", "disableAutoFocus", "disableEnforceFocus", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getTabbable", "isEnabled", "open", "ignoreNextEnforceFocus", "sentinelStart", "sentinelEnd", "nodeToRestore", "reactFocusEventTarget", "activated", "rootRef", "lastKeydown", "contains", "activeElement", "hasAttribute", "setAttribute", "focus", "contain", "nativeEvent", "rootElement", "hasFocus", "target", "tabbable", "_lastKeydown$current", "_lastKeydown$current2", "isShiftTab", "Boolean", "shift<PERSON>ey", "key", "focusNext", "focusPrevious", "loopFocus", "addEventListener", "interval", "setInterval", "clearInterval", "removeEventListener", "handleFocusSentinel", "event", "relatedTarget", "_jsxs", "onFocus", "childrenPropsHandler", "getModalUtilityClass", "slot", "generateUtilityClass", "generateUtilityClasses", "modalUnstyledClasses", "ariaHidden", "element", "show", "removeAttribute", "getPaddingRight", "ownerWindow", "getComputedStyle", "paddingRight", "ariaHiddenSiblings", "mount<PERSON><PERSON>", "currentElement", "elementsToExclude", "blacklist", "call", "isNotExcludedElement", "indexOf", "isNotForbiddenElement", "isForbiddenTagName", "isInputHidden", "isAriaHiddenForbiddenOnElement", "findIndexOf", "items", "idx", "some", "item", "index", "handleContainer", "containerInfo", "restoreStyle", "disableScrollLock", "scrollHeight", "clientHeight", "isOverflowing", "scrollbarSize", "value", "property", "el", "fixedElements", "scrollContainer", "parentNode", "DocumentFragment", "parent", "parentElement", "containerWindow", "overflowY", "overflow", "overflowX", "restore", "_ref", "setProperty", "removeProperty", "defaultManager", "constructor", "containers", "modals", "add", "modal", "modalIndex", "modalRef", "hiddenSiblings", "getHiddenSiblings", "mount", "containerIndex", "remove", "ariaHiddenState", "splice", "nextTop", "isTopModal", "ModalUnstyled", "forwardedRef", "_props$ariaHidden", "classes", "classesProp", "closeAfterTransition", "component", "disableEscapeKeyDown", "hideBackdrop", "keepMounted", "manager", "onBackdropClick", "onClose", "onKeyDown", "onTransitionEnter", "onTransitionExited", "slotProps", "slots", "exited", "setExited", "mountNodeRef", "hasTransition", "hasOwnProperty", "getHasTransition", "ariaHiddenProp", "getModal", "handleMounted", "scrollTop", "handleOpen", "useEventCallback", "resolvedContainer", "handlePortalRef", "handleClose", "backdrop", "composeClasses", "useUtilityClasses", "Root", "rootProps", "useSlotProps", "externalSlotProps", "externalForwardedProps", "additionalProps", "role", "stopPropagation", "className", "BackdropComponent", "backdropProps", "onClick", "currentTarget", "ModalRoot", "styled", "overridesResolver", "hidden", "_ref3", "position", "zIndex", "vars", "right", "bottom", "top", "left", "ModalBackdrop", "Backdrop", "Modal", "inProps", "_slots$root", "_ref2", "_slots$backdrop", "_slotProps$root", "_slotProps$backdrop", "useThemeProps", "BackdropProps", "components", "componentsProps", "commonProps", "extendUtilityClasses", "RootSlot", "BackdropSlot", "rootSlotProps", "backdropSlotProps", "as", "omitEventHandlers", "object", "result", "Object", "keys", "filter", "prop", "match", "mergeSlotProps", "parameters", "getSlotProps", "joinedClasses", "clsx", "mergedStyle", "internalRef", "eventHandlers", "excludeKeys", "includes", "extractEventHandlers", "componentsPropsWithoutEventHandlers", "otherPropsWithoutEventHandlers", "internalSlotProps", "_parameters$additiona", "rest", "resolvedComponentsProps", "mergedProps", "getOverlayAlpha", "elevation", "alphaValue", "log", "toFixed", "getPaperUtilityClass", "paperClasses", "PaperRoot", "variant", "square", "rounded", "_theme$vars$overlays", "backgroundColor", "palette", "background", "paper", "color", "text", "primary", "borderRadius", "shape", "border", "divider", "boxShadow", "shadows", "backgroundImage", "alpha", "overlays", "Paper", "getBackdropUtilityClass", "backdropClasses", "BackdropRoot", "invisible", "display", "alignItems", "justifyContent", "WebkitTapHighlightColor", "transitionDuration"], "mappings": "mGAIe,SAASA,EAAsBC,EAAgBC,GAC5D,MAA8B,oBAAnBD,EACFA,EAAeC,GAEjBD,CACT,CATA,iC,oCCAA,uDAgBe,SAASE,EAAiBC,EAAaC,EAAYH,GAChE,YAAoBI,IAAhBF,GAA6BG,YAAgBH,GACxCC,EAEFG,YAAS,CAAC,EAAGH,EAAY,CAC9BH,WAAYM,YAAS,CAAC,EAAGH,EAAWH,WAAYA,IAEpD,C,oCCvBA,oEAEA,MAAMO,EAAY,CAAC,iBAAkB,SAAU,WAAY,SAAU,KAAM,UAAW,YAAa,aAAc,SAAU,WAAY,YAAa,QAAS,UAAW,uBASlKC,EAAS,CACbC,SAAU,CACRC,QAAS,GAEXC,QAAS,CACPD,QAAS,IAQPE,EAAoBC,cAAiB,SAAcC,EAAOC,GAC9D,MAAMC,EAAQC,cACRC,EAAiB,CACrBC,MAAOH,EAAMI,YAAYC,SAASC,eAClCC,KAAMP,EAAMI,YAAYC,SAASG,gBAE7B,eACFC,EAAc,OACdC,GAAS,EAAI,SACbC,EAAQ,OACRC,EACAC,GAAIC,EAAM,QACVC,EAAO,UACPC,EAAS,WACTC,EAAU,OACVC,EAAM,SACNC,EAAQ,UACRC,EAAS,MACTC,EAAK,QACLC,EAAUpB,EAAc,oBAExBqB,EAAsBC,KACpB1B,EACJ2B,EAAQC,YAA8B5B,EAAOP,GAEzCoC,EAAU9B,SAAa,MACvB+B,EAAYC,YAAWF,EAAShB,EAASZ,IAAKA,GAC9C+B,EAA+BC,GAAYC,IAC/C,GAAID,EAAU,CACZ,MAAME,EAAON,EAAQO,aAGI9C,IAArB4C,EACFD,EAASE,GAETF,EAASE,EAAMD,EAEnB,GAEIG,EAAiBL,EAA6Bb,GAC9CmB,EAAcN,GAA6B,CAACG,EAAMI,KACtDC,YAAOL,GAEP,MAAMM,EAAkBC,YAAmB,CACzCnB,QACAC,UACAV,UACC,CACD6B,KAAM,UAERR,EAAKZ,MAAMqB,iBAAmB1C,EAAMI,YAAYuC,OAAO,UAAWJ,GAClEN,EAAKZ,MAAMuB,WAAa5C,EAAMI,YAAYuC,OAAO,UAAWJ,GACxDxB,GACFA,EAAQkB,EAAMI,EAChB,IAEIQ,EAAgBf,EAA6Bd,GAC7C8B,EAAgBhB,EAA6BV,GAC7C2B,EAAajB,GAA6BG,IAC9C,MAAMM,EAAkBC,YAAmB,CACzCnB,QACAC,UACAV,UACC,CACD6B,KAAM,SAERR,EAAKZ,MAAMqB,iBAAmB1C,EAAMI,YAAYuC,OAAO,UAAWJ,GAClEN,EAAKZ,MAAMuB,WAAa5C,EAAMI,YAAYuC,OAAO,UAAWJ,GACxDrB,GACFA,EAAOe,EACT,IAEIe,EAAelB,EAA6BX,GAOlD,OAAoB8B,cAAK1B,EAAqBjC,YAAS,CACrDoB,OAAQA,EACRG,GAAIC,EACJa,QAAkCA,EAClCZ,QAASqB,EACTpB,UAAW6B,EACX5B,WAAYkB,EACZjB,OAAQ6B,EACR5B,SAAU6B,EACV5B,UAAW0B,EACXrC,eAhB2ByC,IACvBzC,GAEFA,EAAekB,EAAQO,QAASgB,EAClC,EAaA5B,QAASA,GACRG,EAAO,CACRd,SAAUA,CAACwC,EAAOC,IACIvD,eAAmBc,EAAUrB,YAAS,CACxD+B,MAAO/B,YAAS,CACdI,QAAS,EACT2D,WAAsB,WAAVF,GAAuBrC,OAAoB1B,EAAX,UAC3CI,EAAO2D,GAAQ9B,EAAOV,EAASb,MAAMuB,OACxCtB,IAAK6B,GACJwB,MAGT,IA4EexD,K,oCCxMA,SAAS0D,EAAiBC,GAEvC,MAAMC,EAAgBD,EAAIE,gBAAgBC,YAC1C,OAAOC,KAAKC,IAAIC,OAAOC,WAAaN,EACtC,CANA,iC,oCCMe,SAASO,IAAgC,QAAAC,EAAAC,UAAAC,OAAPC,EAAK,IAAAC,MAAAJ,GAAAK,EAAA,EAAAA,EAAAL,EAAAK,IAALF,EAAKE,GAAAJ,UAAAI,GACpD,OAAOF,EAAMG,QAAO,CAACC,EAAKC,IACZ,MAARA,EACKD,EAEF,WAAkC,QAAAE,EAAAR,UAAAC,OAANQ,EAAI,IAAAN,MAAAK,GAAAE,EAAA,EAAAA,EAAAF,EAAAE,IAAJD,EAAIC,GAAAV,UAAAU,GACrCJ,EAAIK,MAAMC,KAAMH,GAChBF,EAAKI,MAAMC,KAAMH,EACnB,IACC,QACL,CAhBA,iC,oCCAA,qDAqBA,MAAMI,EAAsBjF,cAAiB,SAAgBC,EAAOC,GAClE,MAAM,SACJY,EAAQ,UACRoE,EAAS,cACTC,GAAgB,GACdlF,GACGmF,EAAWC,GAAgBrF,WAAe,MAE3C+B,EAAYC,YAAyBhC,iBAAqBc,GAAYA,EAASZ,IAAM,KAAMA,GAejG,GAdAoF,aAAkB,KACXH,GACHE,EA3BN,SAAsBH,GACpB,MAA4B,oBAAdA,EAA2BA,IAAcA,CACzD,CAyBmBK,CAAaL,IAAcM,SAASC,KACnD,GACC,CAACP,EAAWC,IACfG,aAAkB,KAChB,GAAIF,IAAcD,EAEhB,OADAO,YAAOxF,EAAKkF,GACL,KACLM,YAAOxF,EAAK,KAAK,CAGL,GACf,CAACA,EAAKkF,EAAWD,IAChBA,EAAe,CACjB,GAAkBnF,iBAAqBc,GAAW,CAChD,MAAM6E,EAAW,CACfzF,IAAK6B,GAEP,OAAoB/B,eAAmBc,EAAU6E,EACnD,CACA,OAAoBvC,cAAKpD,WAAgB,CACvCc,SAAUA,GAEd,CACA,OAAoBsC,cAAKpD,WAAgB,CACvCc,SAAUsE,EAAyBQ,eAAsB9E,EAAUsE,GAAaA,GAEpF,IA4BeH,K,oCCtFf,oCAOA,MAAMY,EAAqB,CAAC,QAAS,SAAU,WAAY,UAAW,SAAU,aAAc,kBAAmB,kBAAmB,oDAAoDC,KAAK,KAwC7L,SAASC,EAAmBC,GAC1B,MAAMC,EAAkB,GAClBC,EAAkB,GAgBxB,OAfA3B,MAAM4B,KAAKH,EAAKI,iBAAiBP,IAAqBQ,SAAQ,CAACjE,EAAMkE,KACnE,MAAMC,EA3CV,SAAqBnE,GACnB,MAAMoE,EAAeC,SAASrE,EAAKsE,aAAa,aAAe,GAAI,IACnE,OAAKC,OAAOC,MAAMJ,GAYW,SAAzBpE,EAAKyE,kBAAiD,UAAlBzE,EAAK0E,UAA0C,UAAlB1E,EAAK0E,UAA0C,YAAlB1E,EAAK0E,WAA6D,OAAlC1E,EAAKsE,aAAa,YAC3I,EAEFtE,EAAK2E,SAdHP,CAeX,CAyByBQ,CAAY5E,IACX,IAAlBmE,GAXR,SAAyCnE,GACvC,QAAIA,EAAK6E,UAA6B,UAAjB7E,EAAK8E,SAAqC,WAAd9E,EAAK+E,MAfxD,SAA4B/E,GAC1B,GAAqB,UAAjBA,EAAK8E,SAAqC,UAAd9E,EAAK+E,KACnC,OAAO,EAET,IAAK/E,EAAKgF,KACR,OAAO,EAET,MAAMC,EAAWC,GAAYlF,EAAKmF,cAAcC,cAAc,sBAADC,OAAuBH,IACpF,IAAII,EAASL,EAAS,UAADI,OAAWrF,EAAKgF,KAAI,eAIzC,OAHKM,IACHA,EAASL,EAAS,UAADI,OAAWrF,EAAKgF,KAAI,QAEhCM,IAAWtF,CACpB,CAE6EuF,CAAmBvF,GAIhG,CAMgCwF,CAAgCxF,KAGvC,IAAjBmE,EACFN,EAAgB4B,KAAKzF,GAErB8D,EAAgB2B,KAAK,CACnBC,cAAexB,EACfS,SAAUR,EACVnE,KAAMA,IAEV,IAEK8D,EAAgB6B,MAAK,CAACC,EAAGC,IAAMD,EAAEjB,WAAakB,EAAElB,SAAWiB,EAAEF,cAAgBG,EAAEH,cAAgBE,EAAEjB,SAAWkB,EAAElB,WAAUmB,KAAIF,GAAKA,EAAE5F,OAAMqF,OAAOxB,EACzJ,CACA,SAASkC,IACP,OAAO,CACT,CAiQeC,IApPf,SAAmBnI,GACjB,MAAM,SACJa,EAAQ,iBACRuH,GAAmB,EAAK,oBACxBC,GAAsB,EAAK,oBAC3BC,GAAsB,EAAK,YAC3BC,EAAczC,EAAkB,UAChC0C,EAAYN,EAAgB,KAC5BO,GACEzI,EACE0I,EAAyB3I,UAAa,GACtC4I,EAAgB5I,SAAa,MAC7B6I,EAAc7I,SAAa,MAC3B8I,EAAgB9I,SAAa,MAC7B+I,EAAwB/I,SAAa,MAGrCgJ,EAAYhJ,UAAa,GACzBiJ,EAAUjJ,SAAa,MAEvB+B,EAAYC,YAAWlB,EAASZ,IAAK+I,GACrCC,EAAclJ,SAAa,MACjCA,aAAgB,KAET0I,GAASO,EAAQ5G,UAGtB2G,EAAU3G,SAAWgG,EAAgB,GACpC,CAACA,EAAkBK,IACtB1I,aAAgB,KAEd,IAAK0I,IAASO,EAAQ5G,QACpB,OAEF,MAAMqB,EAAM6D,YAAc0B,EAAQ5G,SAYlC,OAXK4G,EAAQ5G,QAAQ8G,SAASzF,EAAI0F,iBAC3BH,EAAQ5G,QAAQgH,aAAa,aAIhCJ,EAAQ5G,QAAQiH,aAAa,WAAY,MAEvCN,EAAU3G,SACZ4G,EAAQ5G,QAAQkH,SAGb,KAEAhB,IAKCO,EAAczG,SAAWyG,EAAczG,QAAQkH,QACjDZ,EAAuBtG,SAAU,EACjCyG,EAAczG,QAAQkH,SAExBT,EAAczG,QAAU,KAC1B,CACD,GAIA,CAACqG,IACJ1I,aAAgB,KAEd,IAAK0I,IAASO,EAAQ5G,QACpB,OAEF,MAAMqB,EAAM6D,YAAc0B,EAAQ5G,SAC5BmH,EAAUC,IACd,MACEpH,QAASqH,GACPT,EAIJ,GAAoB,OAAhBS,EAGJ,GAAKhG,EAAIiG,aAAcrB,GAAwBG,MAAeE,EAAuBtG,SAIrF,IAAKqH,EAAYP,SAASzF,EAAI0F,eAAgB,CAE5C,GAAIK,GAAeV,EAAsB1G,UAAYoH,EAAYG,QAAUlG,EAAI0F,gBAAkBL,EAAsB1G,QACrH0G,EAAsB1G,QAAU,UAC3B,GAAsC,OAAlC0G,EAAsB1G,QAC/B,OAEF,IAAK2G,EAAU3G,QACb,OAEF,IAAIwH,EAAW,GAIf,GAHInG,EAAI0F,gBAAkBR,EAAcvG,SAAWqB,EAAI0F,gBAAkBP,EAAYxG,UACnFwH,EAAWrB,EAAYS,EAAQ5G,UAE7BwH,EAASxF,OAAS,EAAG,CACvB,IAAIyF,EAAsBC,EAC1B,MAAMC,EAAaC,SAAyD,OAA/CH,EAAuBZ,EAAY7G,cAAmB,EAASyH,EAAqBI,WAA8G,SAA/C,OAAhDH,EAAwBb,EAAY7G,cAAmB,EAAS0H,EAAsBI,MAChNC,EAAYP,EAAS,GACrBQ,EAAgBR,EAASA,EAASxF,OAAS,GACxB,kBAAd+F,GAAmD,kBAAlBC,IACtCL,EACFK,EAAcd,QAEda,EAAUb,QAGhB,MACEG,EAAYH,OAEhB,OAhCEZ,EAAuBtG,SAAU,CAgCnC,EAEIiI,EAAYb,IAChBP,EAAY7G,QAAUoH,GAClBnB,GAAwBG,KAAmC,QAApBgB,EAAYU,KAMnDzG,EAAI0F,gBAAkBH,EAAQ5G,SAAWoH,EAAYS,WAGvDvB,EAAuBtG,SAAU,EAC7BwG,EAAYxG,SACdwG,EAAYxG,QAAQkH,QAExB,EAEF7F,EAAI6G,iBAAiB,UAAWf,GAChC9F,EAAI6G,iBAAiB,UAAWD,GAAW,GAQ3C,MAAME,EAAWC,aAAY,KACvB/G,EAAI0F,eAA+C,SAA9B1F,EAAI0F,cAAclC,SACzCsC,EAAQ,KACV,GACC,IACH,MAAO,KACLkB,cAAcF,GACd9G,EAAIiH,oBAAoB,UAAWnB,GACnC9F,EAAIiH,oBAAoB,UAAWL,GAAW,EAAK,CACpD,GACA,CAACjC,EAAkBC,EAAqBC,EAAqBE,EAAWC,EAAMF,IACjF,MAWMoC,EAAsBC,IACI,OAA1B/B,EAAczG,UAChByG,EAAczG,QAAUwI,EAAMC,eAEhC9B,EAAU3G,SAAU,CAAI,EAE1B,OAAoB0I,eAAM/K,WAAgB,CACxCc,SAAU,CAAcsC,cAAK,MAAO,CAClC2D,SAAU2B,EAAO,GAAK,EACtBsC,QAASJ,EACT1K,IAAK0I,EACL,cAAe,kBACA5I,eAAmBc,EAAU,CAC5CZ,IAAK6B,EACLiJ,QAzBYH,IACgB,OAA1B/B,EAAczG,UAChByG,EAAczG,QAAUwI,EAAMC,eAEhC9B,EAAU3G,SAAU,EACpB0G,EAAsB1G,QAAUwI,EAAMjB,OACtC,MAAMqB,EAAuBnK,EAASb,MAAM+K,QACxCC,GACFA,EAAqBJ,EACvB,IAiBiBzH,cAAK,MAAO,CAC3B2D,SAAU2B,EAAO,GAAK,EACtBsC,QAASJ,EACT1K,IAAK2I,EACL,cAAe,kBAGrB,C,gFCzQO,SAASqC,EAAqBC,GACnC,OAAOC,YAAqB,WAAYD,EAC1C,CAC6BE,YAAuB,WAAY,CAAC,OAAQ,WAC1DC,I,2ECGR,SAASC,EAAWC,EAASC,GAC9BA,EACFD,EAAQlC,aAAa,cAAe,QAEpCkC,EAAQE,gBAAgB,cAE5B,CACA,SAASC,EAAgBH,GACvB,OAAO/E,SAASmF,YAAYJ,GAASK,iBAAiBL,GAASM,aAAc,KAAO,CACtF,CAUA,SAASC,EAAmB7G,EAAW8G,EAAcC,EAAgBC,EAAmBT,GACtF,MAAMU,EAAY,CAACH,EAAcC,KAAmBC,GACpD,GAAG7F,QAAQ+F,KAAKlH,EAAUpE,UAAU0K,IAClC,MAAMa,GAAuD,IAAhCF,EAAUG,QAAQd,GACzCe,GAbV,SAAwCf,GAItC,MACMgB,GAAqE,IADjD,CAAC,WAAY,SAAU,QAAS,OAAQ,MAAO,OAAQ,WAAY,UAAW,MAAO,WAAY,QAAS,OAAQ,SAAU,SACzGF,QAAQd,EAAQtE,SACvDuF,EAAoC,UAApBjB,EAAQtE,SAAwD,WAAjCsE,EAAQ9E,aAAa,QAC1E,OAAO8F,GAAsBC,CAC/B,CAKmCC,CAA+BlB,GAC1Da,GAAwBE,GAC1BhB,EAAWC,EAASC,EACtB,GAEJ,CACA,SAASkB,EAAYC,EAAO1K,GAC1B,IAAI2K,GAAO,EAQX,OAPAD,EAAME,MAAK,CAACC,EAAMC,MACZ9K,EAAS6K,KACXF,EAAMG,GACC,KAIJH,CACT,CACA,SAASI,EAAgBC,EAAejN,GACtC,MAAMkN,EAAe,GACfjI,EAAYgI,EAAchI,UAChC,IAAKjF,EAAMmN,kBAAmB,CAC5B,GAnDJ,SAAuBlI,GACrB,MAAMxB,EAAM6D,YAAcrC,GAC1B,OAAIxB,EAAI+B,OAASP,EACR0G,YAAY1G,GAAWjB,WAAaP,EAAIE,gBAAgBC,YAE1DqB,EAAUmI,aAAenI,EAAUoI,YAC5C,CA6CQC,CAAcrI,GAAY,CAE5B,MAAMsI,EAAgB/J,YAAiB8D,YAAcrC,IACrDiI,EAAatF,KAAK,CAChB4F,MAAOvI,EAAU1D,MAAMsK,aACvB4B,SAAU,gBACVC,GAAIzI,IAGNA,EAAU1D,MAAMsK,aAAe,GAAHrE,OAAMkE,EAAgBzG,GAAasI,EAAa,MAG5E,MAAMI,EAAgBrG,YAAcrC,GAAWkB,iBAAiB,cAChE,GAAGC,QAAQ+F,KAAKwB,GAAepC,IAC7B2B,EAAatF,KAAK,CAChB4F,MAAOjC,EAAQhK,MAAMsK,aACrB4B,SAAU,gBACVC,GAAInC,IAENA,EAAQhK,MAAMsK,aAAe,GAAHrE,OAAMkE,EAAgBH,GAAWgC,EAAa,KAAI,GAEhF,CACA,IAAIK,EACJ,GAAI3I,EAAU4I,sBAAsBC,iBAClCF,EAAkBtG,YAAcrC,GAAWO,SACtC,CAGL,MAAMuI,EAAS9I,EAAU+I,cACnBC,EAAkBtC,YAAY1G,GACpC2I,EAAkE,UAArC,MAAVG,OAAiB,EAASA,EAAOlH,WAA+E,WAAvDoH,EAAgBrC,iBAAiBmC,GAAQG,UAAyBH,EAAS9I,CACzJ,CAIAiI,EAAatF,KAAK,CAChB4F,MAAOI,EAAgBrM,MAAM4M,SAC7BV,SAAU,WACVC,GAAIE,GACH,CACDJ,MAAOI,EAAgBrM,MAAM6M,UAC7BX,SAAU,aACVC,GAAIE,GACH,CACDJ,MAAOI,EAAgBrM,MAAM2M,UAC7BT,SAAU,aACVC,GAAIE,IAENA,EAAgBrM,MAAM4M,SAAW,QACnC,CAcA,MAbgBE,KACdnB,EAAa9G,SAAQkI,IAIf,IAJgB,MACpBd,EAAK,GACLE,EAAE,SACFD,GACDa,EACKd,EACFE,EAAGnM,MAAMgN,YAAYd,EAAUD,GAE/BE,EAAGnM,MAAMiN,eAAef,EAC1B,GACA,CAGN,C,+BCnHA,MAAMhO,EAAY,CAAC,WAAY,UAAW,uBAAwB,YAAa,YAAa,mBAAoB,sBAAuB,uBAAwB,gBAAiB,sBAAuB,oBAAqB,eAAgB,cAAe,UAAW,kBAAmB,UAAW,YAAa,OAAQ,oBAAqB,qBAAsB,YAAa,SAiCjX,MAAMgP,EAAiB,IDmGR,MACbC,cACE3J,KAAK4J,gBAAa,EAClB5J,KAAK6J,YAAS,EACd7J,KAAK6J,OAAS,GACd7J,KAAK4J,WAAa,EACpB,CACAE,IAAIC,EAAO7J,GACT,IAAI8J,EAAahK,KAAK6J,OAAOvC,QAAQyC,GACrC,IAAoB,IAAhBC,EACF,OAAOA,EAETA,EAAahK,KAAK6J,OAAOxK,OACzBW,KAAK6J,OAAOhH,KAAKkH,GAGbA,EAAME,UACR1D,EAAWwD,EAAME,UAAU,GAE7B,MAAMC,EAnCV,SAA2BhK,GACzB,MAAMgK,EAAiB,GAMvB,MALA,GAAG7I,QAAQ+F,KAAKlH,EAAUpE,UAAU0K,IACU,SAAxCA,EAAQ9E,aAAa,gBACvBwI,EAAerH,KAAK2D,EACtB,IAEK0D,CACT,CA2B2BC,CAAkBjK,GACzC6G,EAAmB7G,EAAW6J,EAAMK,MAAOL,EAAME,SAAUC,GAAgB,GAC3E,MAAMG,EAAiB1C,EAAY3H,KAAK4J,YAAY7B,GAAQA,EAAK7H,YAAcA,IAC/E,OAAwB,IAApBmK,GACFrK,KAAK4J,WAAWS,GAAgBR,OAAOhH,KAAKkH,GACrCC,IAEThK,KAAK4J,WAAW/G,KAAK,CACnBgH,OAAQ,CAACE,GACT7J,YACAoJ,QAAS,KACTY,mBAEKF,EACT,CACAI,MAAML,EAAO9O,GACX,MAAMoP,EAAiB1C,EAAY3H,KAAK4J,YAAY7B,IAAwC,IAAhCA,EAAK8B,OAAOvC,QAAQyC,KAC1E7B,EAAgBlI,KAAK4J,WAAWS,GACjCnC,EAAcoB,UACjBpB,EAAcoB,QAAUrB,EAAgBC,EAAejN,GAE3D,CACAqP,OAAOP,GAA+B,IAAxBQ,IAAenL,UAAAC,OAAA,QAAA9E,IAAA6E,UAAA,KAAAA,UAAA,GAC3B,MAAM4K,EAAahK,KAAK6J,OAAOvC,QAAQyC,GACvC,IAAoB,IAAhBC,EACF,OAAOA,EAET,MAAMK,EAAiB1C,EAAY3H,KAAK4J,YAAY7B,IAAwC,IAAhCA,EAAK8B,OAAOvC,QAAQyC,KAC1E7B,EAAgBlI,KAAK4J,WAAWS,GAKtC,GAJAnC,EAAc2B,OAAOW,OAAOtC,EAAc2B,OAAOvC,QAAQyC,GAAQ,GACjE/J,KAAK6J,OAAOW,OAAOR,EAAY,GAGK,IAAhC9B,EAAc2B,OAAOxK,OAEnB6I,EAAcoB,SAChBpB,EAAcoB,UAEZS,EAAME,UAER1D,EAAWwD,EAAME,SAAUM,GAE7BxD,EAAmBmB,EAAchI,UAAW6J,EAAMK,MAAOL,EAAME,SAAU/B,EAAcgC,gBAAgB,GACvGlK,KAAK4J,WAAWY,OAAOH,EAAgB,OAClC,CAEL,MAAMI,EAAUvC,EAAc2B,OAAO3B,EAAc2B,OAAOxK,OAAS,GAI/DoL,EAAQR,UACV1D,EAAWkE,EAAQR,UAAU,EAEjC,CACA,OAAOD,CACT,CACAU,WAAWX,GACT,OAAO/J,KAAK6J,OAAOxK,OAAS,GAAKW,KAAK6J,OAAO7J,KAAK6J,OAAOxK,OAAS,KAAO0K,CAC3E,GC6KaY,MAtUoB3P,cAAiB,SAAuBC,EAAO2P,GAChF,IAAIC,EAAmBtB,EACvB,MAAM,SACFzN,EACAgP,QAASC,EAAW,qBACpBC,GAAuB,EAAK,UAC5BC,EAAS,UACT/K,EAAS,iBACTmD,GAAmB,EAAK,oBACxBC,GAAsB,EAAK,qBAC3B4H,GAAuB,EAAK,cAC5B/K,GAAgB,EAAK,oBACrBoD,GAAsB,EAAK,kBAC3B6E,GAAoB,EAAK,aACzB+C,GAAe,EAAK,YACpBC,GAAc,EAAK,QAEnBC,EAAU3B,EAAc,gBACxB4B,EAAe,QACfC,EAAO,UACPC,EAAS,KACT9H,EAAI,kBACJ+H,EAAiB,mBACjBC,EAAkB,UAClBC,EAAY,CAAC,EAAC,MACdC,EAAQ,CAAC,GACP3Q,EACJ2B,EAAQC,YAA8B5B,EAAOP,IACxCmR,EAAQC,GAAa9Q,YAAgB0I,GACtCqG,EAAQ/O,SAAa,CAAC,GACtB+Q,EAAe/Q,SAAa,MAC5BiP,EAAWjP,SAAa,MACxB+B,EAAYC,YAAWiN,EAAUW,GACjCoB,EA9DR,SAA0BlQ,GACxB,QAAOA,GAAWA,EAASb,MAAMgR,eAAe,KAClD,CA4DwBC,CAAiBpQ,GACjCqQ,EAA+D,OAA7CtB,EAAoB5P,EAAM,iBAA0B4P,EAEtEuB,EAAWA,KACfrC,EAAM1M,QAAQ4M,SAAWA,EAAS5M,QAClC0M,EAAM1M,QAAQ+C,UAAY2L,EAAa1O,QAChC0M,EAAM1M,SAETgP,EAAgBA,KACpBhB,EAAQjB,MAAMgC,IAAY,CACxBhE,sBAIE6B,EAAS5M,UACX4M,EAAS5M,QAAQiP,UAAY,EAC/B,EAEIC,EAAaC,aAAiB,KAClC,MAAMC,EApFV,SAAsBvM,GACpB,MAA4B,oBAAdA,EAA2BA,IAAcA,CACzD,CAkF8BK,CAAaL,IAjBpBqC,YAAcwJ,EAAa1O,SAiBgBoD,KAC9D4K,EAAQvB,IAAIsC,IAAYK,GAGpBxC,EAAS5M,SACXgP,GACF,IAEI3B,GAAa1P,eAAkB,IAAMqQ,EAAQX,WAAW0B,MAAa,CAACf,IACtEqB,GAAkBF,aAAiBpP,IACvC2O,EAAa1O,QAAUD,EAClBA,GAAS6M,EAAS5M,UAGnBqG,GAAQgH,KACV2B,IAEA9F,EAAW0D,EAAS5M,QAAS8O,GAC/B,IAEIQ,GAAc3R,eAAkB,KACpCqQ,EAAQf,OAAO8B,IAAYD,EAAe,GACzC,CAACd,EAASc,IACbnR,aAAgB,IACP,KACL2R,IAAa,GAEd,CAACA,KACJ3R,aAAgB,KACV0I,EACF6I,IACUP,GAAkBhB,GAC5B2B,IACF,GACC,CAACjJ,EAAMiJ,GAAaX,EAAehB,EAAsBuB,IAC5D,MAAMpS,GAAaM,YAAS,CAAC,EAAGQ,EAAO,CACrC6P,QAASC,EACTC,uBACA3H,mBACAC,sBACA4H,uBACA/K,gBACAoD,sBACA6E,oBACAyD,SACAV,eACAC,gBAEIN,GAhJkB3Q,KACxB,MAAM,KACJuJ,EAAI,OACJmI,EAAM,QACNf,GACE3Q,EACEyR,EAAQ,CACZ5K,KAAM,CAAC,QAAS0C,GAAQmI,GAAU,UAClCe,SAAU,CAAC,aAEb,OAAOC,YAAejB,EAAO1F,EAAsB4E,EAAQ,EAsI3CgC,CAAkB3S,IAC5BoD,GAAcA,KAClBuO,GAAU,GACNL,GACFA,GACF,EAEItN,GAAeA,KACnB2N,GAAU,GACNJ,GACFA,IAEEV,GACF2B,IACF,EAmCIpO,GAAa,CAAC,OACYhE,IAA5BuB,EAASb,MAAM8G,WACjBxD,GAAWwD,SAAW,MAIpBiK,IACFzN,GAAWrC,QAAUgD,YAAsB3B,GAAazB,EAASb,MAAMiB,SACvEqC,GAAWjC,SAAW4C,YAAsBf,GAAcrC,EAASb,MAAMqB,WAE3E,MAAMyQ,GAA8D,OAAtDxD,EAAoB,MAAb0B,EAAoBA,EAAYW,EAAM5K,MAAgBuI,EAAO,MAC5EyD,GAAYC,YAAa,CAC7B5S,YAAa0S,GACbG,kBAAmBvB,EAAU3K,KAC7BmM,uBAAwBvQ,EACxBwQ,gBAAiB,CACflS,IAAK6B,EACLsQ,KAAM,eACN7B,UAxCkB3F,IAChB2F,GACFA,EAAU3F,GASM,WAAdA,EAAMV,KAAqBuF,OAG1BQ,IAEHrF,EAAMyH,kBACF/B,GACFA,EAAQ1F,EAAO,kBAEnB,GAsBA0H,UAAWzC,GAAQ9J,KACnB7G,gBAEIqT,GAAoB5B,EAAMgB,SAC1Ba,GAAgBR,YAAa,CACjC5S,YAAamT,GACbN,kBAAmBvB,EAAUiB,SAC7BQ,gBAAiB,CACf,eAAe,EACfM,QA9DwB7H,IACtBA,EAAMjB,SAAWiB,EAAM8H,gBAGvBrC,GACFA,EAAgBzF,GAEd0F,GACFA,EAAQ1F,EAAO,iBACjB,EAsDEnC,QAEF6J,UAAWzC,GAAQ8B,SACnBzS,gBAEF,OAAKiR,GAAgB1H,GAAUsI,IAAiBH,EAG5BzN,cAAK6B,IAEvB,CACA/E,IAAKwR,GACLxM,UAAWA,EACXC,cAAeA,EACfrE,SAAuBiK,eAAMgH,GAAMtS,YAAS,CAAC,EAAGuS,GAAW,CACzDlR,SAAU,EAAEqP,GAAgBqC,GAAiCpP,cAAKoP,GAAmB/S,YAAS,CAAC,EAAGgT,KAAkB,KAAmBrP,cAAKgF,IAAW,CACrJE,oBAAqBA,EACrBD,iBAAkBA,EAClBE,oBAAqBA,EACrBE,UAAWiH,GACXhH,KAAMA,EACN5H,SAAuBd,eAAmBc,EAAUyC,YAfjD,IAmBX,I,8CCpQA,MAAM7D,EAAY,CAAC,oBAAqB,gBAAiB,uBAAwB,WAAY,YAAa,aAAc,kBAAmB,mBAAoB,sBAAuB,uBAAwB,gBAAiB,sBAAuB,oBAAqB,eAAgB,cAAe,YAAa,QAAS,SAc1TkT,EAAYC,YAAO,MAAO,CAC9BzL,KAAM,WACN+D,KAAM,OACN2H,kBAAmBA,CAAC7S,EAAON,KACzB,MAAM,WACJR,GACEc,EACJ,MAAO,CAACN,EAAOqG,MAAO7G,EAAWuJ,MAAQvJ,EAAW0R,QAAUlR,EAAOoT,OAAO,GAP9DF,EASfG,IAAA,IAAC,MACF7S,EAAK,WACLhB,GACD6T,EAAA,OAAKvT,YAAS,CACbwT,SAAU,QACVC,QAAS/S,EAAMgT,MAAQhT,GAAO+S,OAAOnE,MACrCqE,MAAO,EACPC,OAAQ,EACRC,IAAK,EACLC,KAAM,IACJpU,EAAWuJ,MAAQvJ,EAAW0R,QAAU,CAC1CrN,WAAY,UACZ,IACIgQ,EAAgBX,YAAOY,IAAU,CACrCrM,KAAM,WACN+D,KAAM,WACN2H,kBAAmBA,CAAC7S,EAAON,IAClBA,EAAOiS,UAJIiB,CAMnB,CACDK,QAAS,IAgBLQ,EAAqB1T,cAAiB,SAAe2T,EAASzT,GAClE,IAAIqO,EAAMqF,EAAaC,EAAOC,EAAiBC,EAAiBC,EAChE,MAAM/T,EAAQgU,YAAc,CAC1B7M,KAAM,WACNnH,MAAO0T,KAEH,kBACFnB,EAAoBgB,EAAa,cACjCU,EAAa,qBACblE,GAAuB,EAAK,SAC5BlP,EAAQ,UACRmP,EAAS,WACTkE,EAAa,CAAC,EAAC,gBACfC,EAAkB,CAAC,EAAC,iBACpB/L,GAAmB,EAAK,oBACxBC,GAAsB,EAAK,qBAC3B4H,GAAuB,EAAK,cAC5B/K,GAAgB,EAAK,oBACrBoD,GAAsB,EAAK,kBAC3B6E,GAAoB,EAAK,aACzB+C,GAAe,EAAK,YACpBC,GAAc,EAAK,UACnBO,EAAS,MACTC,EAAK,MAELzQ,GACEF,EACJ2B,EAAQC,YAA8B5B,EAAOP,IACxCmR,EAAQC,GAAa9Q,YAAe,GACrCqU,EAAc,CAClBrE,uBACA3H,mBACAC,sBACA4H,uBACA/K,gBACAoD,sBACA6E,oBACA+C,eACAC,eAEIjR,EAAaM,YAAS,CAAC,EAAGQ,EAAOoU,EAAa,CAClDxD,WAEIf,EA3FqB3Q,IACpBA,EAAW2Q,QA0FFwE,CAAqBnV,GAC/BoV,EAAmH,OAAvGhG,EAA8D,OAAtDqF,EAAuB,MAAThD,OAAgB,EAASA,EAAM5K,MAAgB4N,EAAcO,EAAWpC,MAAgBxD,EAAOqE,EACjI4B,EAAwI,OAAxHX,EAAuE,OAA9DC,EAA2B,MAATlD,OAAgB,EAASA,EAAMgB,UAAoBkC,EAAkBK,EAAWV,UAAoBI,EAAQrB,EACvJiC,EAAmF,OAAlEV,EAA+B,MAAbpD,OAAoB,EAASA,EAAU3K,MAAgB+N,EAAkBK,EAAgBpO,KAC5H0O,EAA+F,OAA1EV,EAAmC,MAAbrD,OAAoB,EAASA,EAAUiB,UAAoBoC,EAAsBI,EAAgBxC,SAClJ,OAAoBxO,cAAKuM,EAAelQ,YAAS,CAC/CmR,MAAO,CACL5K,KAAMuO,EACN3C,SAAU4C,GAEZ7D,UAAW,CACT3K,KAAMA,IAAMvG,YAAS,CAAC,EAAGR,YAAsBwV,EAAetV,IAAcK,YAAgB+U,IAAa,CACvGI,GAAI1E,EACJ9P,UAEFyR,SAAUA,IAAMnS,YAAS,CAAC,EAAGyU,EAAejV,YAAsByV,EAAmBvV,KAEvFsR,kBAAmBA,IAAMK,GAAU,GACnCJ,mBAAoBA,IAAMI,GAAU,GACpC5Q,IAAKA,GACJ0B,EAAO,CACRkO,QAASA,GACRuE,EAAa,CACdvT,SAAUA,IAEd,IAuKe4S,K,oHCjSA,SAASkB,EAAkBC,GACxC,QAAetV,IAAXsV,EACF,MAAO,CAAC,EAEV,MAAMC,EAAS,CAAC,EAIhB,OAHAC,OAAOC,KAAKH,GAAQI,QAAOC,KAAUA,EAAKC,MAAM,aAAuC,oBAAjBN,EAAOK,MAAuB7O,SAAQ6O,IAC1GJ,EAAOI,GAAQL,EAAOK,EAAK,IAEtBJ,CACT,CCCe,SAASM,EAAeC,GACrC,MAAM,aACJC,EAAY,gBACZlD,EAAe,kBACfF,EAAiB,uBACjBC,EAAsB,UACtBI,GACE8C,EACJ,IAAKC,EAAc,CAGjB,MAAMC,EAAgBC,YAA+B,MAA1BrD,OAAiC,EAASA,EAAuBI,UAAgC,MAArBL,OAA4B,EAASA,EAAkBK,UAAWA,EAA8B,MAAnBH,OAA0B,EAASA,EAAgBG,WACjOkD,EAAchW,YAAS,CAAC,EAAsB,MAAnB2S,OAA0B,EAASA,EAAgB5Q,MAAiC,MAA1B2Q,OAAiC,EAASA,EAAuB3Q,MAA4B,MAArB0Q,OAA4B,EAASA,EAAkB1Q,OACpNvB,EAAQR,YAAS,CAAC,EAAG2S,EAAiBD,EAAwBD,GAOpE,OANIqD,EAAclR,OAAS,IACzBpE,EAAMsS,UAAYgD,GAEhBR,OAAOC,KAAKS,GAAapR,OAAS,IACpCpE,EAAMuB,MAAQiU,GAET,CACLxV,QACAyV,iBAAanW,EAEjB,CAKA,MAAMoW,ECvCO,SAA8Bd,GAA0B,IAAlBe,EAAWxR,UAAAC,OAAA,QAAA9E,IAAA6E,UAAA,GAAAA,UAAA,GAAG,GACjE,QAAe7E,IAAXsV,EACF,MAAO,CAAC,EAEV,MAAMC,EAAS,CAAC,EAIhB,OAHAC,OAAOC,KAAKH,GAAQI,QAAOC,GAAQA,EAAKC,MAAM,aAAuC,oBAAjBN,EAAOK,KAAyBU,EAAYC,SAASX,KAAO7O,SAAQ6O,IACtIJ,EAAOI,GAAQL,EAAOK,EAAK,IAEtBJ,CACT,CD8BwBgB,CAAqBrW,YAAS,CAAC,EAAG0S,EAAwBD,IAC1E6D,EAAsCnB,EAAkB1C,GACxD8D,EAAiCpB,EAAkBzC,GACnD8D,EAAoBX,EAAaK,GAMjCJ,EAAgBC,YAA0B,MAArBS,OAA4B,EAASA,EAAkB1D,UAA8B,MAAnBH,OAA0B,EAASA,EAAgBG,UAAWA,EAAqC,MAA1BJ,OAAiC,EAASA,EAAuBI,UAAgC,MAArBL,OAA4B,EAASA,EAAkBK,WACnSkD,EAAchW,YAAS,CAAC,EAAwB,MAArBwW,OAA4B,EAASA,EAAkBzU,MAA0B,MAAnB4Q,OAA0B,EAASA,EAAgB5Q,MAAiC,MAA1B2Q,OAAiC,EAASA,EAAuB3Q,MAA4B,MAArB0Q,OAA4B,EAASA,EAAkB1Q,OAClRvB,EAAQR,YAAS,CAAC,EAAGwW,EAAmB7D,EAAiB4D,EAAgCD,GAO/F,OANIR,EAAclR,OAAS,IACzBpE,EAAMsS,UAAYgD,GAEhBR,OAAOC,KAAKS,GAAapR,OAAS,IACpCpE,EAAMuB,MAAQiU,GAET,CACLxV,QACAyV,YAAaO,EAAkB/V,IAEnC,C,cElEA,MAAMR,EAAY,CAAC,cAAe,oBAAqB,cAYxC,SAASuS,EAAaoD,GACnC,IAAIa,EACJ,MAAM,YACF7W,EAAW,kBACX6S,EAAiB,WACjB/S,GACEkW,EACJc,EAAOtU,YAA8BwT,EAAY3V,GAC7C0W,EAA0BnX,YAAsBiT,EAAmB/S,IAEvEc,MAAOoW,EAAW,YAClBX,GACEN,EAAe3V,YAAS,CAAC,EAAG0W,EAAM,CACpCjE,kBAAmBkE,KAEflW,EAAM8B,YAAW0T,EAAwC,MAA3BU,OAAkC,EAASA,EAAwBlW,IAA6D,OAAvDgW,EAAwBb,EAAWjD,sBAA2B,EAAS8D,EAAsBhW,KAI1M,OAHcd,YAAiBC,EAAaI,YAAS,CAAC,EAAG4W,EAAa,CACpEnW,QACEf,EAEN,C,gGCxBemX,MATSC,IACtB,IAAIC,EAMJ,OAJEA,EADED,EAAY,EACD,QAAUA,GAAa,EAEvB,IAAMzS,KAAK2S,IAAIF,EAAY,GAAK,GAEvCC,EAAa,KAAKE,QAAQ,EAAE,E,0BCN/B,SAASC,EAAqBxL,GACnC,OAAOC,YAAqB,WAAYD,EAC1C,CACqBE,YAAuB,WAAY,CAAC,OAAQ,UAAW,WAAY,YAAa,aAAc,aAAc,aAAc,aAAc,aAAc,aAAc,aAAc,aAAc,aAAc,aAAc,cAAe,cAAe,cAAe,cAAe,cAAe,cAAe,cAAe,cAAe,cAAe,cAAe,cAAe,cAAe,cAAe,cAAe,gBACpbuL,I,OCJf,MAAMlX,EAAY,CAAC,YAAa,YAAa,YAAa,SAAU,WAyB9DmX,EAAYhE,YAAO,MAAO,CAC9BzL,KAAM,WACN+D,KAAM,OACN2H,kBAAmBA,CAAC7S,EAAON,KACzB,MAAM,WACJR,GACEc,EACJ,MAAO,CAACN,EAAOqG,KAAMrG,EAAOR,EAAW2X,UAAW3X,EAAW4X,QAAUpX,EAAOqX,QAAgC,cAAvB7X,EAAW2X,SAA2BnX,EAAO,YAAD8H,OAAatI,EAAWoX,YAAa,GAP1J1D,EASftE,IAGG,IAHF,MACFpO,EAAK,WACLhB,GACDoP,EACC,IAAI0I,EACJ,OAAOxX,YAAS,CACdyX,iBAAkB/W,EAAMgT,MAAQhT,GAAOgX,QAAQC,WAAWC,MAC1DC,OAAQnX,EAAMgT,MAAQhT,GAAOgX,QAAQI,KAAKC,QAC1CzU,WAAY5C,EAAMI,YAAYuC,OAAO,gBACnC3D,EAAW4X,QAAU,CACvBU,aAActX,EAAMuX,MAAMD,cACF,aAAvBtY,EAAW2X,SAA0B,CACtCa,OAAQ,aAAFlQ,QAAgBtH,EAAMgT,MAAQhT,GAAOgX,QAAQS,UAC3B,cAAvBzY,EAAW2X,SAA2BrX,YAAS,CAChDoY,WAAY1X,EAAMgT,MAAQhT,GAAO2X,QAAQ3Y,EAAWoX,aAClDpW,EAAMgT,MAA+B,SAAvBhT,EAAMgX,QAAQvU,MAAmB,CACjDmV,gBAAiB,mBAAFtQ,OAAqBuQ,YAAM,OAAQ1B,EAAgBnX,EAAWoX,YAAW,MAAA9O,OAAKuQ,YAAM,OAAQ1B,EAAgBnX,EAAWoX,YAAW,MAChJpW,EAAMgT,MAAQ,CACf4E,gBAAiE,OAA/Cd,EAAuB9W,EAAMgT,KAAK8E,eAAoB,EAAShB,EAAqB9X,EAAWoX,aAChH,IAEC2B,EAAqBlY,cAAiB,SAAe2T,EAASzT,GAClE,MAAMD,EAAQgU,YAAc,CAC1BhU,MAAO0T,EACPvM,KAAM,cAEF,UACFmL,EAAS,UACTtC,EAAY,MAAK,UACjBsG,EAAY,EAAC,OACbQ,GAAS,EAAK,QACdD,EAAU,aACR7W,EACJ2B,EAAQC,YAA8B5B,EAAOP,GACzCP,EAAaM,YAAS,CAAC,EAAGQ,EAAO,CACrCgQ,YACAsG,YACAQ,SACAD,YAEIhH,EA7DkB3Q,KACxB,MAAM,OACJ4X,EAAM,UACNR,EAAS,QACTO,EAAO,QACPhH,GACE3Q,EACEyR,EAAQ,CACZ5K,KAAM,CAAC,OAAQ8Q,GAAUC,GAAU,UAAuB,cAAZD,GAA2B,YAAJrP,OAAgB8O,KAEvF,OAAO1E,YAAejB,EAAO+F,EAAsB7G,EAAQ,EAmD3CgC,CAAkB3S,GAQlC,OAAoBiE,cAAKyT,EAAWpX,YAAS,CAC3CkV,GAAI1E,EACJ9Q,WAAYA,EACZoT,UAAWiD,YAAK1F,EAAQ9J,KAAMuM,GAC9BrS,IAAKA,GACJ0B,GACL,IAqDesW,K,2HC7IR,SAASC,EAAwBhN,GACtC,OAAOC,YAAqB,cAAeD,EAC7C,CACwBE,YAAuB,cAAe,CAAC,OAAQ,cACxD+M,I,OCJf,MAAM1Y,EAAY,CAAC,WAAY,YAAa,aAAc,kBAAmB,YAAa,YAAa,OAAQ,YAAa,QAAS,qBAAsB,uBAoBrJ2Y,EAAexF,YAAO,MAAO,CACjCzL,KAAM,cACN+D,KAAM,OACN2H,kBAAmBA,CAAC7S,EAAON,KACzB,MAAM,WACJR,GACEc,EACJ,MAAO,CAACN,EAAOqG,KAAM7G,EAAWmZ,WAAa3Y,EAAO2Y,UAAU,GAP7CzF,EASlBgB,IAAA,IAAC,WACF1U,GACD0U,EAAA,OAAKpU,YAAS,CACbwT,SAAU,QACVsF,QAAS,OACTC,WAAY,SACZC,eAAgB,SAChBrF,MAAO,EACPC,OAAQ,EACRC,IAAK,EACLC,KAAM,EACN2D,gBAAiB,qBACjBwB,wBAAyB,eACxBvZ,EAAWmZ,WAAa,CACzBpB,gBAAiB,eACjB,IACIzD,EAAwBzT,cAAiB,SAAkB2T,EAASzT,GACxE,IAAI6T,EAAiBxF,EAAMqF,EAC3B,MAAM3T,EAAQgU,YAAc,CAC1BhU,MAAO0T,EACPvM,KAAM,iBAEF,SACFtG,EAAQ,UACRmP,EAAY,MAAK,WACjBkE,EAAa,CAAC,EAAC,gBACfC,EAAkB,CAAC,EAAC,UACpB7B,EAAS,UACT+F,GAAY,EAAK,KACjB5P,EAAI,UACJiI,EAAY,CAAC,EAAC,MACdC,EAAQ,CAAC,EAAC,mBACV+H,EAAkB,oBAElBjX,EAAsB3B,KACpBE,EACJ2B,EAAQC,YAA8B5B,EAAOP,GACzCP,EAAaM,YAAS,CAAC,EAAGQ,EAAO,CACrCgQ,YACAqI,cAEIxI,EA5DkB3Q,KACxB,MAAM,QACJ2Q,EAAO,UACPwI,GACEnZ,EACEyR,EAAQ,CACZ5K,KAAM,CAAC,OAAQsS,GAAa,cAE9B,OAAOzG,YAAejB,EAAOuH,EAAyBrI,EAAQ,EAoD9CgC,CAAkB3S,GAC5BsV,EAAsD,OAArCV,EAAkBpD,EAAU3K,MAAgB+N,EAAkBK,EAAgBpO,KACrG,OAAoB5C,cAAK1B,EAAqBjC,YAAS,CACrDuB,GAAI0H,EACJjH,QAASkX,GACR/W,EAAO,CACRd,SAAuBsC,cAAKiV,EAAc5Y,YAAS,CACjD,eAAe,GACdgV,EAAe,CAChBE,GAAmF,OAA9EpG,EAAqC,OAA7BqF,EAAchD,EAAM5K,MAAgB4N,EAAcO,EAAWpC,MAAgBxD,EAAO0B,EACjGsC,UAAWiD,YAAK1F,EAAQ9J,KAAMuM,EAA4B,MAAjBkC,OAAwB,EAASA,EAAclC,WACxFpT,WAAYM,YAAS,CAAC,EAAGN,EAA6B,MAAjBsV,OAAwB,EAASA,EAActV,YACpF2Q,QAASA,EACT5P,IAAKA,EACLY,SAAUA,OAGhB,IA2Fe2S,K", "file": "static/js/1.04d537b3.chunk.js", "sourcesContent": ["/**\n * If `componentProps` is a function, calls it with the provided `ownerState`.\n * Otherwise, just returns `componentProps`.\n */\nexport default function resolveComponentProps(componentProps, ownerState) {\n  if (typeof componentProps === 'function') {\n    return componentProps(ownerState);\n  }\n  return componentProps;\n}", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport isHostComponent from './isHostComponent';\n\n/**\n * Type of the ownerState based on the type of an element it applies to.\n * This resolves to the provided OwnerState for React components and `undefined` for host components.\n * Falls back to `OwnerState | undefined` when the exact type can't be determined in development time.\n */\n\n/**\n * Appends the ownerState object to the props, merging with the existing one if necessary.\n *\n * @param elementType Type of the element that owns the `existingProps`. If the element is a DOM node or undefined, `ownerState` is not applied.\n * @param otherProps Props of the element.\n * @param ownerState\n */\nexport default function appendOwnerState(elementType, otherProps, ownerState) {\n  if (elementType === undefined || isHostComponent(elementType)) {\n    return otherProps;\n  }\n  return _extends({}, otherProps, {\n    ownerState: _extends({}, otherProps.ownerState, ownerState)\n  });\n}", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"addEndListener\", \"appear\", \"children\", \"easing\", \"in\", \"onEnter\", \"onEntered\", \"onEntering\", \"onExit\", \"onExited\", \"onExiting\", \"style\", \"timeout\", \"TransitionComponent\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { Transition } from 'react-transition-group';\nimport { elementAcceptingRef } from '@mui/utils';\nimport useTheme from '../styles/useTheme';\nimport { reflow, getTransitionProps } from '../transitions/utils';\nimport useForkRef from '../utils/useForkRef';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst styles = {\n  entering: {\n    opacity: 1\n  },\n  entered: {\n    opacity: 1\n  }\n};\n\n/**\n * The Fade transition is used by the [Modal](/material-ui/react-modal/) component.\n * It uses [react-transition-group](https://github.com/reactjs/react-transition-group) internally.\n */\nconst Fade = /*#__PURE__*/React.forwardRef(function Fade(props, ref) {\n  const theme = useTheme();\n  const defaultTimeout = {\n    enter: theme.transitions.duration.enteringScreen,\n    exit: theme.transitions.duration.leavingScreen\n  };\n  const {\n      addEndListener,\n      appear = true,\n      children,\n      easing,\n      in: inProp,\n      onEnter,\n      onEntered,\n      onEntering,\n      onExit,\n      onExited,\n      onExiting,\n      style,\n      timeout = defaultTimeout,\n      // eslint-disable-next-line react/prop-types\n      TransitionComponent = Transition\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const enableStrictModeCompat = true;\n  const nodeRef = React.useRef(null);\n  const handleRef = useForkRef(nodeRef, children.ref, ref);\n  const normalizedTransitionCallback = callback => maybeIsAppearing => {\n    if (callback) {\n      const node = nodeRef.current;\n\n      // onEnterXxx and onExitXxx callbacks have a different arguments.length value.\n      if (maybeIsAppearing === undefined) {\n        callback(node);\n      } else {\n        callback(node, maybeIsAppearing);\n      }\n    }\n  };\n  const handleEntering = normalizedTransitionCallback(onEntering);\n  const handleEnter = normalizedTransitionCallback((node, isAppearing) => {\n    reflow(node); // So the animation always start from the start.\n\n    const transitionProps = getTransitionProps({\n      style,\n      timeout,\n      easing\n    }, {\n      mode: 'enter'\n    });\n    node.style.webkitTransition = theme.transitions.create('opacity', transitionProps);\n    node.style.transition = theme.transitions.create('opacity', transitionProps);\n    if (onEnter) {\n      onEnter(node, isAppearing);\n    }\n  });\n  const handleEntered = normalizedTransitionCallback(onEntered);\n  const handleExiting = normalizedTransitionCallback(onExiting);\n  const handleExit = normalizedTransitionCallback(node => {\n    const transitionProps = getTransitionProps({\n      style,\n      timeout,\n      easing\n    }, {\n      mode: 'exit'\n    });\n    node.style.webkitTransition = theme.transitions.create('opacity', transitionProps);\n    node.style.transition = theme.transitions.create('opacity', transitionProps);\n    if (onExit) {\n      onExit(node);\n    }\n  });\n  const handleExited = normalizedTransitionCallback(onExited);\n  const handleAddEndListener = next => {\n    if (addEndListener) {\n      // Old call signature before `react-transition-group` implemented `nodeRef`\n      addEndListener(nodeRef.current, next);\n    }\n  };\n  return /*#__PURE__*/_jsx(TransitionComponent, _extends({\n    appear: appear,\n    in: inProp,\n    nodeRef: enableStrictModeCompat ? nodeRef : undefined,\n    onEnter: handleEnter,\n    onEntered: handleEntered,\n    onEntering: handleEntering,\n    onExit: handleExit,\n    onExited: handleExited,\n    onExiting: handleExiting,\n    addEndListener: handleAddEndListener,\n    timeout: timeout\n  }, other, {\n    children: (state, childProps) => {\n      return /*#__PURE__*/React.cloneElement(children, _extends({\n        style: _extends({\n          opacity: 0,\n          visibility: state === 'exited' && !inProp ? 'hidden' : undefined\n        }, styles[state], style, children.props.style),\n        ref: handleRef\n      }, childProps));\n    }\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Fade.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n  /**\n   * Add a custom transition end trigger. Called with the transitioning DOM\n   * node and a done callback. Allows for more fine grained transition end\n   * logic. Note: Timeouts are still used as a fallback if provided.\n   */\n  addEndListener: PropTypes.func,\n  /**\n   * Perform the enter transition when it first mounts if `in` is also `true`.\n   * Set this to `false` to disable this behavior.\n   * @default true\n   */\n  appear: PropTypes.bool,\n  /**\n   * A single child content element.\n   */\n  children: elementAcceptingRef.isRequired,\n  /**\n   * The transition timing function.\n   * You may specify a single easing or a object containing enter and exit values.\n   */\n  easing: PropTypes.oneOfType([PropTypes.shape({\n    enter: PropTypes.string,\n    exit: PropTypes.string\n  }), PropTypes.string]),\n  /**\n   * If `true`, the component will transition in.\n   */\n  in: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  onEnter: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onEntered: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onEntering: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onExit: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onExited: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onExiting: PropTypes.func,\n  /**\n   * @ignore\n   */\n  style: PropTypes.object,\n  /**\n   * The duration for the transition, in milliseconds.\n   * You may specify a single timeout for all transitions, or individually with an object.\n   * @default {\n   *   enter: theme.transitions.duration.enteringScreen,\n   *   exit: theme.transitions.duration.leavingScreen,\n   * }\n   */\n  timeout: PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })])\n} : void 0;\nexport default Fade;", "// A change of the browser zoom change the scrollbar size.\n// Credit https://github.com/twbs/bootstrap/blob/488fd8afc535ca3a6ad4dc581f5e89217b6a36ac/js/src/util/scrollbar.js#L14-L18\nexport default function getScrollbarSize(doc) {\n  // https://developer.mozilla.org/en-US/docs/Web/API/Window/innerWidth#usage_notes\n  const documentWidth = doc.documentElement.clientWidth;\n  return Math.abs(window.innerWidth - documentWidth);\n}", "/**\n * Safe chained function.\n *\n * Will only create a new function if needed,\n * otherwise will pass back existing functions or null.\n */\nexport default function createChainedFunction(...funcs) {\n  return funcs.reduce((acc, func) => {\n    if (func == null) {\n      return acc;\n    }\n    return function chainedFunction(...args) {\n      acc.apply(this, args);\n      func.apply(this, args);\n    };\n  }, () => {});\n}", "import * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport PropTypes from 'prop-types';\nimport { exactProp, HTMLElementType, unstable_useEnhancedEffect as useEnhancedEffect, unstable_useForkRef as useForkRef, unstable_setRef as setRef } from '@mui/utils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction getContainer(container) {\n  return typeof container === 'function' ? container() : container;\n}\n\n/**\n * Portals provide a first-class way to render children into a DOM node\n * that exists outside the DOM hierarchy of the parent component.\n *\n * Demos:\n *\n * - [Portal](https://mui.com/base/react-portal/)\n *\n * API:\n *\n * - [Portal API](https://mui.com/base/api/portal/)\n */\nconst Portal = /*#__PURE__*/React.forwardRef(function Portal(props, ref) {\n  const {\n    children,\n    container,\n    disablePortal = false\n  } = props;\n  const [mountNode, setMountNode] = React.useState(null);\n  // @ts-expect-error TODO upstream fix\n  const handleRef = useForkRef( /*#__PURE__*/React.isValidElement(children) ? children.ref : null, ref);\n  useEnhancedEffect(() => {\n    if (!disablePortal) {\n      setMountNode(getContainer(container) || document.body);\n    }\n  }, [container, disablePortal]);\n  useEnhancedEffect(() => {\n    if (mountNode && !disablePortal) {\n      setRef(ref, mountNode);\n      return () => {\n        setRef(ref, null);\n      };\n    }\n    return undefined;\n  }, [ref, mountNode, disablePortal]);\n  if (disablePortal) {\n    if ( /*#__PURE__*/React.isValidElement(children)) {\n      const newProps = {\n        ref: handleRef\n      };\n      return /*#__PURE__*/React.cloneElement(children, newProps);\n    }\n    return /*#__PURE__*/_jsx(React.Fragment, {\n      children: children\n    });\n  }\n  return /*#__PURE__*/_jsx(React.Fragment, {\n    children: mountNode ? /*#__PURE__*/ReactDOM.createPortal(children, mountNode) : mountNode\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Portal.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * The children to render into the `container`.\n   */\n  children: PropTypes.node,\n  /**\n   * An HTML element or function that returns one.\n   * The `container` will have the portal children appended to it.\n   *\n   * By default, it uses the body of the top-level document object,\n   * so it's simply `document.body` most of the time.\n   */\n  container: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([HTMLElementType, PropTypes.func]),\n  /**\n   * The `children` will be under the DOM hierarchy of the parent component.\n   * @default false\n   */\n  disablePortal: PropTypes.bool\n} : void 0;\nif (process.env.NODE_ENV !== 'production') {\n  // eslint-disable-next-line\n  Portal['propTypes' + ''] = exactProp(Portal.propTypes);\n}\nexport default Portal;", "/* eslint-disable consistent-return, jsx-a11y/no-noninteractive-tabindex */\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { exactProp, elementAcceptingRef, unstable_useForkRef as useForkRef, unstable_ownerDocument as ownerDocument } from '@mui/utils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\n// Inspired by https://github.com/focus-trap/tabbable\nconst candidatesSelector = ['input', 'select', 'textarea', 'a[href]', 'button', '[tabindex]', 'audio[controls]', 'video[controls]', '[contenteditable]:not([contenteditable=\"false\"])'].join(',');\nfunction getTabIndex(node) {\n  const tabindexAttr = parseInt(node.getAttribute('tabindex') || '', 10);\n  if (!Number.isNaN(tabindexAttr)) {\n    return tabindexAttr;\n  }\n\n  // Browsers do not return `tabIndex` correctly for contentEditable nodes;\n  // https://bugs.chromium.org/p/chromium/issues/detail?id=661108&q=contenteditable%20tabindex&can=2\n  // so if they don't have a tabindex attribute specifically set, assume it's 0.\n  // in Chrome, <details/>, <audio controls/> and <video controls/> elements get a default\n  //  `tabIndex` of -1 when the 'tabindex' attribute isn't specified in the DOM,\n  //  yet they are still part of the regular tab order; in FF, they get a default\n  //  `tabIndex` of 0; since Chrome still puts those elements in the regular tab\n  //  order, consider their tab index to be 0.\n  if (node.contentEditable === 'true' || (node.nodeName === 'AUDIO' || node.nodeName === 'VIDEO' || node.nodeName === 'DETAILS') && node.getAttribute('tabindex') === null) {\n    return 0;\n  }\n  return node.tabIndex;\n}\nfunction isNonTabbableRadio(node) {\n  if (node.tagName !== 'INPUT' || node.type !== 'radio') {\n    return false;\n  }\n  if (!node.name) {\n    return false;\n  }\n  const getRadio = selector => node.ownerDocument.querySelector(`input[type=\"radio\"]${selector}`);\n  let roving = getRadio(`[name=\"${node.name}\"]:checked`);\n  if (!roving) {\n    roving = getRadio(`[name=\"${node.name}\"]`);\n  }\n  return roving !== node;\n}\nfunction isNodeMatchingSelectorFocusable(node) {\n  if (node.disabled || node.tagName === 'INPUT' && node.type === 'hidden' || isNonTabbableRadio(node)) {\n    return false;\n  }\n  return true;\n}\nfunction defaultGetTabbable(root) {\n  const regularTabNodes = [];\n  const orderedTabNodes = [];\n  Array.from(root.querySelectorAll(candidatesSelector)).forEach((node, i) => {\n    const nodeTabIndex = getTabIndex(node);\n    if (nodeTabIndex === -1 || !isNodeMatchingSelectorFocusable(node)) {\n      return;\n    }\n    if (nodeTabIndex === 0) {\n      regularTabNodes.push(node);\n    } else {\n      orderedTabNodes.push({\n        documentOrder: i,\n        tabIndex: nodeTabIndex,\n        node: node\n      });\n    }\n  });\n  return orderedTabNodes.sort((a, b) => a.tabIndex === b.tabIndex ? a.documentOrder - b.documentOrder : a.tabIndex - b.tabIndex).map(a => a.node).concat(regularTabNodes);\n}\nfunction defaultIsEnabled() {\n  return true;\n}\n\n/**\n * Utility component that locks focus inside the component.\n *\n * Demos:\n *\n * - [Focus Trap](https://mui.com/base/react-focus-trap/)\n *\n * API:\n *\n * - [FocusTrap API](https://mui.com/base/api/focus-trap/)\n */\nfunction FocusTrap(props) {\n  const {\n    children,\n    disableAutoFocus = false,\n    disableEnforceFocus = false,\n    disableRestoreFocus = false,\n    getTabbable = defaultGetTabbable,\n    isEnabled = defaultIsEnabled,\n    open\n  } = props;\n  const ignoreNextEnforceFocus = React.useRef(false);\n  const sentinelStart = React.useRef(null);\n  const sentinelEnd = React.useRef(null);\n  const nodeToRestore = React.useRef(null);\n  const reactFocusEventTarget = React.useRef(null);\n  // This variable is useful when disableAutoFocus is true.\n  // It waits for the active element to move into the component to activate.\n  const activated = React.useRef(false);\n  const rootRef = React.useRef(null);\n  // @ts-expect-error TODO upstream fix\n  const handleRef = useForkRef(children.ref, rootRef);\n  const lastKeydown = React.useRef(null);\n  React.useEffect(() => {\n    // We might render an empty child.\n    if (!open || !rootRef.current) {\n      return;\n    }\n    activated.current = !disableAutoFocus;\n  }, [disableAutoFocus, open]);\n  React.useEffect(() => {\n    // We might render an empty child.\n    if (!open || !rootRef.current) {\n      return;\n    }\n    const doc = ownerDocument(rootRef.current);\n    if (!rootRef.current.contains(doc.activeElement)) {\n      if (!rootRef.current.hasAttribute('tabIndex')) {\n        if (process.env.NODE_ENV !== 'production') {\n          console.error(['MUI: The modal content node does not accept focus.', 'For the benefit of assistive technologies, ' + 'the tabIndex of the node is being set to \"-1\".'].join('\\n'));\n        }\n        rootRef.current.setAttribute('tabIndex', '-1');\n      }\n      if (activated.current) {\n        rootRef.current.focus();\n      }\n    }\n    return () => {\n      // restoreLastFocus()\n      if (!disableRestoreFocus) {\n        // In IE11 it is possible for document.activeElement to be null resulting\n        // in nodeToRestore.current being null.\n        // Not all elements in IE11 have a focus method.\n        // Once IE11 support is dropped the focus() call can be unconditional.\n        if (nodeToRestore.current && nodeToRestore.current.focus) {\n          ignoreNextEnforceFocus.current = true;\n          nodeToRestore.current.focus();\n        }\n        nodeToRestore.current = null;\n      }\n    };\n    // Missing `disableRestoreFocus` which is fine.\n    // We don't support changing that prop on an open FocusTrap\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [open]);\n  React.useEffect(() => {\n    // We might render an empty child.\n    if (!open || !rootRef.current) {\n      return;\n    }\n    const doc = ownerDocument(rootRef.current);\n    const contain = nativeEvent => {\n      const {\n        current: rootElement\n      } = rootRef;\n\n      // Cleanup functions are executed lazily in React 17.\n      // Contain can be called between the component being unmounted and its cleanup function being run.\n      if (rootElement === null) {\n        return;\n      }\n      if (!doc.hasFocus() || disableEnforceFocus || !isEnabled() || ignoreNextEnforceFocus.current) {\n        ignoreNextEnforceFocus.current = false;\n        return;\n      }\n      if (!rootElement.contains(doc.activeElement)) {\n        // if the focus event is not coming from inside the children's react tree, reset the refs\n        if (nativeEvent && reactFocusEventTarget.current !== nativeEvent.target || doc.activeElement !== reactFocusEventTarget.current) {\n          reactFocusEventTarget.current = null;\n        } else if (reactFocusEventTarget.current !== null) {\n          return;\n        }\n        if (!activated.current) {\n          return;\n        }\n        let tabbable = [];\n        if (doc.activeElement === sentinelStart.current || doc.activeElement === sentinelEnd.current) {\n          tabbable = getTabbable(rootRef.current);\n        }\n        if (tabbable.length > 0) {\n          var _lastKeydown$current, _lastKeydown$current2;\n          const isShiftTab = Boolean(((_lastKeydown$current = lastKeydown.current) == null ? void 0 : _lastKeydown$current.shiftKey) && ((_lastKeydown$current2 = lastKeydown.current) == null ? void 0 : _lastKeydown$current2.key) === 'Tab');\n          const focusNext = tabbable[0];\n          const focusPrevious = tabbable[tabbable.length - 1];\n          if (typeof focusNext !== 'string' && typeof focusPrevious !== 'string') {\n            if (isShiftTab) {\n              focusPrevious.focus();\n            } else {\n              focusNext.focus();\n            }\n          }\n        } else {\n          rootElement.focus();\n        }\n      }\n    };\n    const loopFocus = nativeEvent => {\n      lastKeydown.current = nativeEvent;\n      if (disableEnforceFocus || !isEnabled() || nativeEvent.key !== 'Tab') {\n        return;\n      }\n\n      // Make sure the next tab starts from the right place.\n      // doc.activeElement referes to the origin.\n      if (doc.activeElement === rootRef.current && nativeEvent.shiftKey) {\n        // We need to ignore the next contain as\n        // it will try to move the focus back to the rootRef element.\n        ignoreNextEnforceFocus.current = true;\n        if (sentinelEnd.current) {\n          sentinelEnd.current.focus();\n        }\n      }\n    };\n    doc.addEventListener('focusin', contain);\n    doc.addEventListener('keydown', loopFocus, true);\n\n    // With Edge, Safari and Firefox, no focus related events are fired when the focused area stops being a focused area.\n    // e.g. https://bugzilla.mozilla.org/show_bug.cgi?id=559561.\n    // Instead, we can look if the active element was restored on the BODY element.\n    //\n    // The whatwg spec defines how the browser should behave but does not explicitly mention any events:\n    // https://html.spec.whatwg.org/multipage/interaction.html#focus-fixup-rule.\n    const interval = setInterval(() => {\n      if (doc.activeElement && doc.activeElement.tagName === 'BODY') {\n        contain(null);\n      }\n    }, 50);\n    return () => {\n      clearInterval(interval);\n      doc.removeEventListener('focusin', contain);\n      doc.removeEventListener('keydown', loopFocus, true);\n    };\n  }, [disableAutoFocus, disableEnforceFocus, disableRestoreFocus, isEnabled, open, getTabbable]);\n  const onFocus = event => {\n    if (nodeToRestore.current === null) {\n      nodeToRestore.current = event.relatedTarget;\n    }\n    activated.current = true;\n    reactFocusEventTarget.current = event.target;\n    const childrenPropsHandler = children.props.onFocus;\n    if (childrenPropsHandler) {\n      childrenPropsHandler(event);\n    }\n  };\n  const handleFocusSentinel = event => {\n    if (nodeToRestore.current === null) {\n      nodeToRestore.current = event.relatedTarget;\n    }\n    activated.current = true;\n  };\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(\"div\", {\n      tabIndex: open ? 0 : -1,\n      onFocus: handleFocusSentinel,\n      ref: sentinelStart,\n      \"data-testid\": \"sentinelStart\"\n    }), /*#__PURE__*/React.cloneElement(children, {\n      ref: handleRef,\n      onFocus\n    }), /*#__PURE__*/_jsx(\"div\", {\n      tabIndex: open ? 0 : -1,\n      onFocus: handleFocusSentinel,\n      ref: sentinelEnd,\n      \"data-testid\": \"sentinelEnd\"\n    })]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? FocusTrap.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * A single child content element.\n   */\n  children: elementAcceptingRef,\n  /**\n   * If `true`, the focus trap will not automatically shift focus to itself when it opens, and\n   * replace it to the last focused element when it closes.\n   * This also works correctly with any focus trap children that have the `disableAutoFocus` prop.\n   *\n   * Generally this should never be set to `true` as it makes the focus trap less\n   * accessible to assistive technologies, like screen readers.\n   * @default false\n   */\n  disableAutoFocus: PropTypes.bool,\n  /**\n   * If `true`, the focus trap will not prevent focus from leaving the focus trap while open.\n   *\n   * Generally this should never be set to `true` as it makes the focus trap less\n   * accessible to assistive technologies, like screen readers.\n   * @default false\n   */\n  disableEnforceFocus: PropTypes.bool,\n  /**\n   * If `true`, the focus trap will not restore focus to previously focused element once\n   * focus trap is hidden or unmounted.\n   * @default false\n   */\n  disableRestoreFocus: PropTypes.bool,\n  /**\n   * Returns an array of ordered tabbable nodes (i.e. in tab order) within the root.\n   * For instance, you can provide the \"tabbable\" npm dependency.\n   * @param {HTMLElement} root\n   */\n  getTabbable: PropTypes.func,\n  /**\n   * This prop extends the `open` prop.\n   * It allows to toggle the open state without having to wait for a rerender when changing the `open` prop.\n   * This prop should be memoized.\n   * It can be used to support multiple focus trap mounted at the same time.\n   * @default function defaultIsEnabled(): boolean {\n   *   return true;\n   * }\n   */\n  isEnabled: PropTypes.func,\n  /**\n   * If `true`, focus is locked.\n   */\n  open: PropTypes.bool.isRequired\n} : void 0;\nif (process.env.NODE_ENV !== 'production') {\n  // eslint-disable-next-line\n  FocusTrap['propTypes' + ''] = exactProp(FocusTrap.propTypes);\n}\nexport default FocusTrap;", "import generateUtilityClasses from '../generateUtilityClasses';\nimport generateUtilityClass from '../generateUtilityClass';\nexport function getModalUtilityClass(slot) {\n  return generateUtilityClass('MuiModal', slot);\n}\nconst modalUnstyledClasses = generateUtilityClasses('MuiModal', ['root', 'hidden']);\nexport default modalUnstyledClasses;", "import { unstable_ownerWindow as ownerWindow, unstable_ownerDocument as ownerDocument, unstable_getScrollbarSize as getScrollbarSize } from '@mui/utils';\n// Is a vertical scrollbar displayed?\nfunction isOverflowing(container) {\n  const doc = ownerDocument(container);\n  if (doc.body === container) {\n    return ownerWindow(container).innerWidth > doc.documentElement.clientWidth;\n  }\n  return container.scrollHeight > container.clientHeight;\n}\nexport function ariaHidden(element, show) {\n  if (show) {\n    element.setAttribute('aria-hidden', 'true');\n  } else {\n    element.removeAttribute('aria-hidden');\n  }\n}\nfunction getPaddingRight(element) {\n  return parseInt(ownerWindow(element).getComputedStyle(element).paddingRight, 10) || 0;\n}\nfunction isAriaHiddenForbiddenOnElement(element) {\n  // The forbidden HTML tags are the ones from ARIA specification that\n  // can be children of body and can't have aria-hidden attribute.\n  // cf. https://www.w3.org/TR/html-aria/#docconformance\n  const forbiddenTagNames = ['TEMPLATE', 'SCRIPT', 'STYLE', 'LINK', 'MAP', 'META', 'NOSCRIPT', 'PICTURE', 'COL', 'COLGROUP', 'PARAM', 'SLOT', 'SOURCE', 'TRACK'];\n  const isForbiddenTagName = forbiddenTagNames.indexOf(element.tagName) !== -1;\n  const isInputHidden = element.tagName === 'INPUT' && element.getAttribute('type') === 'hidden';\n  return isForbiddenTagName || isInputHidden;\n}\nfunction ariaHiddenSiblings(container, mountElement, currentElement, elementsToExclude, show) {\n  const blacklist = [mountElement, currentElement, ...elementsToExclude];\n  [].forEach.call(container.children, element => {\n    const isNotExcludedElement = blacklist.indexOf(element) === -1;\n    const isNotForbiddenElement = !isAriaHiddenForbiddenOnElement(element);\n    if (isNotExcludedElement && isNotForbiddenElement) {\n      ariaHidden(element, show);\n    }\n  });\n}\nfunction findIndexOf(items, callback) {\n  let idx = -1;\n  items.some((item, index) => {\n    if (callback(item)) {\n      idx = index;\n      return true;\n    }\n    return false;\n  });\n  return idx;\n}\nfunction handleContainer(containerInfo, props) {\n  const restoreStyle = [];\n  const container = containerInfo.container;\n  if (!props.disableScrollLock) {\n    if (isOverflowing(container)) {\n      // Compute the size before applying overflow hidden to avoid any scroll jumps.\n      const scrollbarSize = getScrollbarSize(ownerDocument(container));\n      restoreStyle.push({\n        value: container.style.paddingRight,\n        property: 'padding-right',\n        el: container\n      });\n      // Use computed style, here to get the real padding to add our scrollbar width.\n      container.style.paddingRight = `${getPaddingRight(container) + scrollbarSize}px`;\n\n      // .mui-fixed is a global helper.\n      const fixedElements = ownerDocument(container).querySelectorAll('.mui-fixed');\n      [].forEach.call(fixedElements, element => {\n        restoreStyle.push({\n          value: element.style.paddingRight,\n          property: 'padding-right',\n          el: element\n        });\n        element.style.paddingRight = `${getPaddingRight(element) + scrollbarSize}px`;\n      });\n    }\n    let scrollContainer;\n    if (container.parentNode instanceof DocumentFragment) {\n      scrollContainer = ownerDocument(container).body;\n    } else {\n      // Improve Gatsby support\n      // https://css-tricks.com/snippets/css/force-vertical-scrollbar/\n      const parent = container.parentElement;\n      const containerWindow = ownerWindow(container);\n      scrollContainer = (parent == null ? void 0 : parent.nodeName) === 'HTML' && containerWindow.getComputedStyle(parent).overflowY === 'scroll' ? parent : container;\n    }\n\n    // Block the scroll even if no scrollbar is visible to account for mobile keyboard\n    // screensize shrink.\n    restoreStyle.push({\n      value: scrollContainer.style.overflow,\n      property: 'overflow',\n      el: scrollContainer\n    }, {\n      value: scrollContainer.style.overflowX,\n      property: 'overflow-x',\n      el: scrollContainer\n    }, {\n      value: scrollContainer.style.overflowY,\n      property: 'overflow-y',\n      el: scrollContainer\n    });\n    scrollContainer.style.overflow = 'hidden';\n  }\n  const restore = () => {\n    restoreStyle.forEach(({\n      value,\n      el,\n      property\n    }) => {\n      if (value) {\n        el.style.setProperty(property, value);\n      } else {\n        el.style.removeProperty(property);\n      }\n    });\n  };\n  return restore;\n}\nfunction getHiddenSiblings(container) {\n  const hiddenSiblings = [];\n  [].forEach.call(container.children, element => {\n    if (element.getAttribute('aria-hidden') === 'true') {\n      hiddenSiblings.push(element);\n    }\n  });\n  return hiddenSiblings;\n}\n/**\n * @ignore - do not document.\n *\n * Proper state management for containers and the modals in those containers.\n * Simplified, but inspired by react-overlay's ModalManager class.\n * Used by the Modal to ensure proper styling of containers.\n */\nexport default class ModalManager {\n  constructor() {\n    this.containers = void 0;\n    this.modals = void 0;\n    this.modals = [];\n    this.containers = [];\n  }\n  add(modal, container) {\n    let modalIndex = this.modals.indexOf(modal);\n    if (modalIndex !== -1) {\n      return modalIndex;\n    }\n    modalIndex = this.modals.length;\n    this.modals.push(modal);\n\n    // If the modal we are adding is already in the DOM.\n    if (modal.modalRef) {\n      ariaHidden(modal.modalRef, false);\n    }\n    const hiddenSiblings = getHiddenSiblings(container);\n    ariaHiddenSiblings(container, modal.mount, modal.modalRef, hiddenSiblings, true);\n    const containerIndex = findIndexOf(this.containers, item => item.container === container);\n    if (containerIndex !== -1) {\n      this.containers[containerIndex].modals.push(modal);\n      return modalIndex;\n    }\n    this.containers.push({\n      modals: [modal],\n      container,\n      restore: null,\n      hiddenSiblings\n    });\n    return modalIndex;\n  }\n  mount(modal, props) {\n    const containerIndex = findIndexOf(this.containers, item => item.modals.indexOf(modal) !== -1);\n    const containerInfo = this.containers[containerIndex];\n    if (!containerInfo.restore) {\n      containerInfo.restore = handleContainer(containerInfo, props);\n    }\n  }\n  remove(modal, ariaHiddenState = true) {\n    const modalIndex = this.modals.indexOf(modal);\n    if (modalIndex === -1) {\n      return modalIndex;\n    }\n    const containerIndex = findIndexOf(this.containers, item => item.modals.indexOf(modal) !== -1);\n    const containerInfo = this.containers[containerIndex];\n    containerInfo.modals.splice(containerInfo.modals.indexOf(modal), 1);\n    this.modals.splice(modalIndex, 1);\n\n    // If that was the last modal in a container, clean up the container.\n    if (containerInfo.modals.length === 0) {\n      // The modal might be closed before it had the chance to be mounted in the DOM.\n      if (containerInfo.restore) {\n        containerInfo.restore();\n      }\n      if (modal.modalRef) {\n        // In case the modal wasn't in the DOM yet.\n        ariaHidden(modal.modalRef, ariaHiddenState);\n      }\n      ariaHiddenSiblings(containerInfo.container, modal.mount, modal.modalRef, containerInfo.hiddenSiblings, false);\n      this.containers.splice(containerIndex, 1);\n    } else {\n      // Otherwise make sure the next top modal is visible to a screen reader.\n      const nextTop = containerInfo.modals[containerInfo.modals.length - 1];\n      // as soon as a modal is adding its modalRef is undefined. it can't set\n      // aria-hidden because the dom element doesn't exist either\n      // when modal was unmounted before modalRef gets null\n      if (nextTop.modalRef) {\n        ariaHidden(nextTop.modalRef, false);\n      }\n    }\n    return modalIndex;\n  }\n  isTopModal(modal) {\n    return this.modals.length > 0 && this.modals[this.modals.length - 1] === modal;\n  }\n}", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"classes\", \"closeAfterTransition\", \"component\", \"container\", \"disableAutoFocus\", \"disableEnforceFocus\", \"disableEscapeKeyDown\", \"disablePortal\", \"disableRestoreFocus\", \"disableScrollLock\", \"hideBackdrop\", \"keepMounted\", \"manager\", \"onBackdropClick\", \"onClose\", \"onKeyDown\", \"open\", \"onTransitionEnter\", \"onTransitionExited\", \"slotProps\", \"slots\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { elementAcceptingRef, HTMLElementType, unstable_ownerDocument as ownerDocument, unstable_useForkRef as useForkRef, unstable_createChainedFunction as createChainedFunction, unstable_useEventCallback as useEventCallback } from '@mui/utils';\nimport composeClasses from '../composeClasses';\nimport Portal from '../Portal';\nimport ModalManager, { ariaHidden } from './ModalManager';\nimport FocusTrap from '../FocusTrap';\nimport { getModalUtilityClass } from './modalUnstyledClasses';\nimport { useSlotProps } from '../utils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    open,\n    exited,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', !open && exited && 'hidden'],\n    backdrop: ['backdrop']\n  };\n  return composeClasses(slots, getModalUtilityClass, classes);\n};\nfunction getContainer(container) {\n  return typeof container === 'function' ? container() : container;\n}\nfunction getHasTransition(children) {\n  return children ? children.props.hasOwnProperty('in') : false;\n}\n\n// A modal manager used to track and manage the state of open Modals.\n// Modals don't open on the server so this won't conflict with concurrent requests.\nconst defaultManager = new ModalManager();\n\n/**\n * Modal is a lower-level construct that is leveraged by the following components:\n *\n * *   [Dialog](https://mui.com/material-ui/api/dialog/)\n * *   [Drawer](https://mui.com/material-ui/api/drawer/)\n * *   [Menu](https://mui.com/material-ui/api/menu/)\n * *   [Popover](https://mui.com/material-ui/api/popover/)\n *\n * If you are creating a modal dialog, you probably want to use the [Dialog](https://mui.com/material-ui/api/dialog/) component\n * rather than directly using Modal.\n *\n * This component shares many concepts with [react-overlays](https://react-bootstrap.github.io/react-overlays/#modals).\n *\n * Demos:\n *\n * - [Unstyled Modal](https://mui.com/base/react-modal/)\n *\n * API:\n *\n * - [ModalUnstyled API](https://mui.com/base/api/modal-unstyled/)\n */\nconst ModalUnstyled = /*#__PURE__*/React.forwardRef(function ModalUnstyled(props, forwardedRef) {\n  var _props$ariaHidden, _ref;\n  const {\n      children,\n      classes: classesProp,\n      closeAfterTransition = false,\n      component,\n      container,\n      disableAutoFocus = false,\n      disableEnforceFocus = false,\n      disableEscapeKeyDown = false,\n      disablePortal = false,\n      disableRestoreFocus = false,\n      disableScrollLock = false,\n      hideBackdrop = false,\n      keepMounted = false,\n      // private\n      manager = defaultManager,\n      onBackdropClick,\n      onClose,\n      onKeyDown,\n      open,\n      onTransitionEnter,\n      onTransitionExited,\n      slotProps = {},\n      slots = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const [exited, setExited] = React.useState(!open);\n  const modal = React.useRef({});\n  const mountNodeRef = React.useRef(null);\n  const modalRef = React.useRef(null);\n  const handleRef = useForkRef(modalRef, forwardedRef);\n  const hasTransition = getHasTransition(children);\n  const ariaHiddenProp = (_props$ariaHidden = props['aria-hidden']) != null ? _props$ariaHidden : true;\n  const getDoc = () => ownerDocument(mountNodeRef.current);\n  const getModal = () => {\n    modal.current.modalRef = modalRef.current;\n    modal.current.mountNode = mountNodeRef.current;\n    return modal.current;\n  };\n  const handleMounted = () => {\n    manager.mount(getModal(), {\n      disableScrollLock\n    });\n\n    // Fix a bug on Chrome where the scroll isn't initially 0.\n    if (modalRef.current) {\n      modalRef.current.scrollTop = 0;\n    }\n  };\n  const handleOpen = useEventCallback(() => {\n    const resolvedContainer = getContainer(container) || getDoc().body;\n    manager.add(getModal(), resolvedContainer);\n\n    // The element was already mounted.\n    if (modalRef.current) {\n      handleMounted();\n    }\n  });\n  const isTopModal = React.useCallback(() => manager.isTopModal(getModal()), [manager]);\n  const handlePortalRef = useEventCallback(node => {\n    mountNodeRef.current = node;\n    if (!node || !modalRef.current) {\n      return;\n    }\n    if (open && isTopModal()) {\n      handleMounted();\n    } else {\n      ariaHidden(modalRef.current, ariaHiddenProp);\n    }\n  });\n  const handleClose = React.useCallback(() => {\n    manager.remove(getModal(), ariaHiddenProp);\n  }, [manager, ariaHiddenProp]);\n  React.useEffect(() => {\n    return () => {\n      handleClose();\n    };\n  }, [handleClose]);\n  React.useEffect(() => {\n    if (open) {\n      handleOpen();\n    } else if (!hasTransition || !closeAfterTransition) {\n      handleClose();\n    }\n  }, [open, handleClose, hasTransition, closeAfterTransition, handleOpen]);\n  const ownerState = _extends({}, props, {\n    classes: classesProp,\n    closeAfterTransition,\n    disableAutoFocus,\n    disableEnforceFocus,\n    disableEscapeKeyDown,\n    disablePortal,\n    disableRestoreFocus,\n    disableScrollLock,\n    exited,\n    hideBackdrop,\n    keepMounted\n  });\n  const classes = useUtilityClasses(ownerState);\n  const handleEnter = () => {\n    setExited(false);\n    if (onTransitionEnter) {\n      onTransitionEnter();\n    }\n  };\n  const handleExited = () => {\n    setExited(true);\n    if (onTransitionExited) {\n      onTransitionExited();\n    }\n    if (closeAfterTransition) {\n      handleClose();\n    }\n  };\n  const handleBackdropClick = event => {\n    if (event.target !== event.currentTarget) {\n      return;\n    }\n    if (onBackdropClick) {\n      onBackdropClick(event);\n    }\n    if (onClose) {\n      onClose(event, 'backdropClick');\n    }\n  };\n  const handleKeyDown = event => {\n    if (onKeyDown) {\n      onKeyDown(event);\n    }\n\n    // The handler doesn't take event.defaultPrevented into account:\n    //\n    // event.preventDefault() is meant to stop default behaviors like\n    // clicking a checkbox to check it, hitting a button to submit a form,\n    // and hitting left arrow to move the cursor in a text input etc.\n    // Only special HTML elements have these default behaviors.\n    if (event.key !== 'Escape' || !isTopModal()) {\n      return;\n    }\n    if (!disableEscapeKeyDown) {\n      // Swallow the event, in case someone is listening for the escape key on the body.\n      event.stopPropagation();\n      if (onClose) {\n        onClose(event, 'escapeKeyDown');\n      }\n    }\n  };\n  const childProps = {};\n  if (children.props.tabIndex === undefined) {\n    childProps.tabIndex = '-1';\n  }\n\n  // It's a Transition like component\n  if (hasTransition) {\n    childProps.onEnter = createChainedFunction(handleEnter, children.props.onEnter);\n    childProps.onExited = createChainedFunction(handleExited, children.props.onExited);\n  }\n  const Root = (_ref = component != null ? component : slots.root) != null ? _ref : 'div';\n  const rootProps = useSlotProps({\n    elementType: Root,\n    externalSlotProps: slotProps.root,\n    externalForwardedProps: other,\n    additionalProps: {\n      ref: handleRef,\n      role: 'presentation',\n      onKeyDown: handleKeyDown\n    },\n    className: classes.root,\n    ownerState\n  });\n  const BackdropComponent = slots.backdrop;\n  const backdropProps = useSlotProps({\n    elementType: BackdropComponent,\n    externalSlotProps: slotProps.backdrop,\n    additionalProps: {\n      'aria-hidden': true,\n      onClick: handleBackdropClick,\n      open\n    },\n    className: classes.backdrop,\n    ownerState\n  });\n  if (!keepMounted && !open && (!hasTransition || exited)) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(Portal\n  // @ts-expect-error TODO: include ref to MUI Base Portal props\n  , {\n    ref: handlePortalRef,\n    container: container,\n    disablePortal: disablePortal,\n    children: /*#__PURE__*/_jsxs(Root, _extends({}, rootProps, {\n      children: [!hideBackdrop && BackdropComponent ? /*#__PURE__*/_jsx(BackdropComponent, _extends({}, backdropProps)) : null, /*#__PURE__*/_jsx(FocusTrap, {\n        disableEnforceFocus: disableEnforceFocus,\n        disableAutoFocus: disableAutoFocus,\n        disableRestoreFocus: disableRestoreFocus,\n        isEnabled: isTopModal,\n        open: open,\n        children: /*#__PURE__*/React.cloneElement(children, childProps)\n      })]\n    }))\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? ModalUnstyled.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * A single child content element.\n   */\n  children: elementAcceptingRef.isRequired,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * When set to true the Modal waits until a nested Transition is completed before closing.\n   * @default false\n   */\n  closeAfterTransition: PropTypes.bool,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * An HTML element or function that returns one.\n   * The `container` will have the portal children appended to it.\n   *\n   * By default, it uses the body of the top-level document object,\n   * so it's simply `document.body` most of the time.\n   */\n  container: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([HTMLElementType, PropTypes.func]),\n  /**\n   * If `true`, the modal will not automatically shift focus to itself when it opens, and\n   * replace it to the last focused element when it closes.\n   * This also works correctly with any modal children that have the `disableAutoFocus` prop.\n   *\n   * Generally this should never be set to `true` as it makes the modal less\n   * accessible to assistive technologies, like screen readers.\n   * @default false\n   */\n  disableAutoFocus: PropTypes.bool,\n  /**\n   * If `true`, the modal will not prevent focus from leaving the modal while open.\n   *\n   * Generally this should never be set to `true` as it makes the modal less\n   * accessible to assistive technologies, like screen readers.\n   * @default false\n   */\n  disableEnforceFocus: PropTypes.bool,\n  /**\n   * If `true`, hitting escape will not fire the `onClose` callback.\n   * @default false\n   */\n  disableEscapeKeyDown: PropTypes.bool,\n  /**\n   * The `children` will be under the DOM hierarchy of the parent component.\n   * @default false\n   */\n  disablePortal: PropTypes.bool,\n  /**\n   * If `true`, the modal will not restore focus to previously focused element once\n   * modal is hidden or unmounted.\n   * @default false\n   */\n  disableRestoreFocus: PropTypes.bool,\n  /**\n   * Disable the scroll lock behavior.\n   * @default false\n   */\n  disableScrollLock: PropTypes.bool,\n  /**\n   * If `true`, the backdrop is not rendered.\n   * @default false\n   */\n  hideBackdrop: PropTypes.bool,\n  /**\n   * Always keep the children in the DOM.\n   * This prop can be useful in SEO situation or\n   * when you want to maximize the responsiveness of the Modal.\n   * @default false\n   */\n  keepMounted: PropTypes.bool,\n  /**\n   * Callback fired when the backdrop is clicked.\n   * @deprecated Use the `onClose` prop with the `reason` argument to handle the `backdropClick` events.\n   */\n  onBackdropClick: PropTypes.func,\n  /**\n   * Callback fired when the component requests to be closed.\n   * The `reason` parameter can optionally be used to control the response to `onClose`.\n   *\n   * @param {object} event The event source of the callback.\n   * @param {string} reason Can be: `\"escapeKeyDown\"`, `\"backdropClick\"`.\n   */\n  onClose: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onKeyDown: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool.isRequired,\n  /**\n   * The props used for each slot inside the Modal.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    backdrop: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside the Modal.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    backdrop: PropTypes.elementType,\n    root: PropTypes.elementType\n  })\n} : void 0;\nexport default ModalUnstyled;", "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"BackdropComponent\", \"BackdropProps\", \"closeAfterTransition\", \"children\", \"component\", \"components\", \"componentsProps\", \"disableAutoFocus\", \"disableEnforceFocus\", \"disableEscapeKeyDown\", \"disablePortal\", \"disableRestoreFocus\", \"disableScrollLock\", \"hideBackdrop\", \"keepMounted\", \"slotProps\", \"slots\", \"theme\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport ModalUnstyled, { modalUnstyledClasses } from '@mui/base/ModalUnstyled';\nimport { isHostComponent, resolveComponentProps } from '@mui/base/utils';\nimport { elementAcceptingRef, HTMLElementType } from '@mui/utils';\nimport styled from '../styles/styled';\nimport useThemeProps from '../styles/useThemeProps';\nimport Backdrop from '../Backdrop';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const modalClasses = modalUnstyledClasses;\nconst extendUtilityClasses = ownerState => {\n  return ownerState.classes;\n};\nconst ModalRoot = styled('div', {\n  name: 'MuiModal',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, !ownerState.open && ownerState.exited && styles.hidden];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  position: 'fixed',\n  zIndex: (theme.vars || theme).zIndex.modal,\n  right: 0,\n  bottom: 0,\n  top: 0,\n  left: 0\n}, !ownerState.open && ownerState.exited && {\n  visibility: 'hidden'\n}));\nconst ModalBackdrop = styled(Backdrop, {\n  name: 'MuiModal',\n  slot: 'Backdrop',\n  overridesResolver: (props, styles) => {\n    return styles.backdrop;\n  }\n})({\n  zIndex: -1\n});\n\n/**\n * Modal is a lower-level construct that is leveraged by the following components:\n *\n * - [Dialog](/material-ui/api/dialog/)\n * - [Drawer](/material-ui/api/drawer/)\n * - [Menu](/material-ui/api/menu/)\n * - [Popover](/material-ui/api/popover/)\n *\n * If you are creating a modal dialog, you probably want to use the [Dialog](/material-ui/api/dialog/) component\n * rather than directly using Modal.\n *\n * This component shares many concepts with [react-overlays](https://react-bootstrap.github.io/react-overlays/#modals).\n */\nconst Modal = /*#__PURE__*/React.forwardRef(function Modal(inProps, ref) {\n  var _ref, _slots$root, _ref2, _slots$backdrop, _slotProps$root, _slotProps$backdrop;\n  const props = useThemeProps({\n    name: 'MuiModal',\n    props: inProps\n  });\n  const {\n      BackdropComponent = ModalBackdrop,\n      BackdropProps,\n      closeAfterTransition = false,\n      children,\n      component,\n      components = {},\n      componentsProps = {},\n      disableAutoFocus = false,\n      disableEnforceFocus = false,\n      disableEscapeKeyDown = false,\n      disablePortal = false,\n      disableRestoreFocus = false,\n      disableScrollLock = false,\n      hideBackdrop = false,\n      keepMounted = false,\n      slotProps,\n      slots,\n      // eslint-disable-next-line react/prop-types\n      theme\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const [exited, setExited] = React.useState(true);\n  const commonProps = {\n    closeAfterTransition,\n    disableAutoFocus,\n    disableEnforceFocus,\n    disableEscapeKeyDown,\n    disablePortal,\n    disableRestoreFocus,\n    disableScrollLock,\n    hideBackdrop,\n    keepMounted\n  };\n  const ownerState = _extends({}, props, commonProps, {\n    exited\n  });\n  const classes = extendUtilityClasses(ownerState);\n  const RootSlot = (_ref = (_slots$root = slots == null ? void 0 : slots.root) != null ? _slots$root : components.Root) != null ? _ref : ModalRoot;\n  const BackdropSlot = (_ref2 = (_slots$backdrop = slots == null ? void 0 : slots.backdrop) != null ? _slots$backdrop : components.Backdrop) != null ? _ref2 : BackdropComponent;\n  const rootSlotProps = (_slotProps$root = slotProps == null ? void 0 : slotProps.root) != null ? _slotProps$root : componentsProps.root;\n  const backdropSlotProps = (_slotProps$backdrop = slotProps == null ? void 0 : slotProps.backdrop) != null ? _slotProps$backdrop : componentsProps.backdrop;\n  return /*#__PURE__*/_jsx(ModalUnstyled, _extends({\n    slots: {\n      root: RootSlot,\n      backdrop: BackdropSlot\n    },\n    slotProps: {\n      root: () => _extends({}, resolveComponentProps(rootSlotProps, ownerState), !isHostComponent(RootSlot) && {\n        as: component,\n        theme\n      }),\n      backdrop: () => _extends({}, BackdropProps, resolveComponentProps(backdropSlotProps, ownerState))\n    },\n    onTransitionEnter: () => setExited(false),\n    onTransitionExited: () => setExited(true),\n    ref: ref\n  }, other, {\n    classes: classes\n  }, commonProps, {\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Modal.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n  /**\n   * A backdrop component. This prop enables custom backdrop rendering.\n   * @deprecated Use `slots.backdrop` instead. While this prop currently works, it will be removed in the next major version.\n   * Use the `slots.backdrop` prop to make your application ready for the next version of Material UI.\n   * @default styled(Backdrop, {\n   *   name: 'MuiModal',\n   *   slot: 'Backdrop',\n   *   overridesResolver: (props, styles) => {\n   *     return styles.backdrop;\n   *   },\n   * })({\n   *   zIndex: -1,\n   * })\n   */\n  BackdropComponent: PropTypes.elementType,\n  /**\n   * Props applied to the [`Backdrop`](/material-ui/api/backdrop/) element.\n   * @deprecated Use `slotProps.backdrop` instead.\n   */\n  BackdropProps: PropTypes.object,\n  /**\n   * A single child content element.\n   */\n  children: elementAcceptingRef.isRequired,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * When set to true the Modal waits until a nested Transition is completed before closing.\n   * @default false\n   */\n  closeAfterTransition: PropTypes.bool,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside.\n   *\n   * This prop is an alias for the `slots` prop.\n   * It's recommended to use the `slots` prop instead.\n   *\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Backdrop: PropTypes.elementType,\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `slotProps` prop.\n   * It's recommended to use the `slotProps` prop instead, as `componentsProps` will be deprecated in the future.\n   *\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    backdrop: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * An HTML element or function that returns one.\n   * The `container` will have the portal children appended to it.\n   *\n   * By default, it uses the body of the top-level document object,\n   * so it's simply `document.body` most of the time.\n   */\n  container: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([HTMLElementType, PropTypes.func]),\n  /**\n   * If `true`, the modal will not automatically shift focus to itself when it opens, and\n   * replace it to the last focused element when it closes.\n   * This also works correctly with any modal children that have the `disableAutoFocus` prop.\n   *\n   * Generally this should never be set to `true` as it makes the modal less\n   * accessible to assistive technologies, like screen readers.\n   * @default false\n   */\n  disableAutoFocus: PropTypes.bool,\n  /**\n   * If `true`, the modal will not prevent focus from leaving the modal while open.\n   *\n   * Generally this should never be set to `true` as it makes the modal less\n   * accessible to assistive technologies, like screen readers.\n   * @default false\n   */\n  disableEnforceFocus: PropTypes.bool,\n  /**\n   * If `true`, hitting escape will not fire the `onClose` callback.\n   * @default false\n   */\n  disableEscapeKeyDown: PropTypes.bool,\n  /**\n   * The `children` will be under the DOM hierarchy of the parent component.\n   * @default false\n   */\n  disablePortal: PropTypes.bool,\n  /**\n   * If `true`, the modal will not restore focus to previously focused element once\n   * modal is hidden or unmounted.\n   * @default false\n   */\n  disableRestoreFocus: PropTypes.bool,\n  /**\n   * Disable the scroll lock behavior.\n   * @default false\n   */\n  disableScrollLock: PropTypes.bool,\n  /**\n   * If `true`, the backdrop is not rendered.\n   * @default false\n   */\n  hideBackdrop: PropTypes.bool,\n  /**\n   * Always keep the children in the DOM.\n   * This prop can be useful in SEO situation or\n   * when you want to maximize the responsiveness of the Modal.\n   * @default false\n   */\n  keepMounted: PropTypes.bool,\n  /**\n   * Callback fired when the backdrop is clicked.\n   * @deprecated Use the `onClose` prop with the `reason` argument to handle the `backdropClick` events.\n   */\n  onBackdropClick: PropTypes.func,\n  /**\n   * Callback fired when the component requests to be closed.\n   * The `reason` parameter can optionally be used to control the response to `onClose`.\n   *\n   * @param {object} event The event source of the callback.\n   * @param {string} reason Can be: `\"escapeKeyDown\"`, `\"backdropClick\"`.\n   */\n  onClose: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool.isRequired,\n  /**\n   * The props used for each slot inside the Modal.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    backdrop: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside the Modal.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    backdrop: PropTypes.elementType,\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Modal;", "/**\n * Removes event handlers from the given object.\n * A field is considered an event handler if it is a function with a name beginning with `on`.\n *\n * @param object Object to remove event handlers from.\n * @returns Object with event handlers removed.\n */\nexport default function omitEventHandlers(object) {\n  if (object === undefined) {\n    return {};\n  }\n  const result = {};\n  Object.keys(object).filter(prop => !(prop.match(/^on[A-Z]/) && typeof object[prop] === 'function')).forEach(prop => {\n    result[prop] = object[prop];\n  });\n  return result;\n}", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport clsx from 'clsx';\nimport extractEventHandlers from './extractEventHandlers';\nimport omitEventHandlers from './omitEventHandlers';\n/**\n * Merges the slot component internal props (usually coming from a hook)\n * with the externally provided ones.\n *\n * The merge order is (the latter overrides the former):\n * 1. The internal props (specified as a getter function to work with get*Props hook result)\n * 2. Additional props (specified internally on an unstyled component)\n * 3. External props specified on the owner component. These should only be used on a root slot.\n * 4. External props specified in the `slotProps.*` prop.\n * 5. The `className` prop - combined from all the above.\n * @param parameters\n * @returns\n */\nexport default function mergeSlotProps(parameters) {\n  const {\n    getSlotProps,\n    additionalProps,\n    externalSlotProps,\n    externalForwardedProps,\n    className\n  } = parameters;\n  if (!getSlotProps) {\n    // The simpler case - getSlotProps is not defined, so no internal event handlers are defined,\n    // so we can simply merge all the props without having to worry about extracting event handlers.\n    const joinedClasses = clsx(externalForwardedProps == null ? void 0 : externalForwardedProps.className, externalSlotProps == null ? void 0 : externalSlotProps.className, className, additionalProps == null ? void 0 : additionalProps.className);\n    const mergedStyle = _extends({}, additionalProps == null ? void 0 : additionalProps.style, externalForwardedProps == null ? void 0 : externalForwardedProps.style, externalSlotProps == null ? void 0 : externalSlotProps.style);\n    const props = _extends({}, additionalProps, externalForwardedProps, externalSlotProps);\n    if (joinedClasses.length > 0) {\n      props.className = joinedClasses;\n    }\n    if (Object.keys(mergedStyle).length > 0) {\n      props.style = mergedStyle;\n    }\n    return {\n      props,\n      internalRef: undefined\n    };\n  }\n\n  // In this case, getSlotProps is responsible for calling the external event handlers.\n  // We don't need to include them in the merged props because of this.\n\n  const eventHandlers = extractEventHandlers(_extends({}, externalForwardedProps, externalSlotProps));\n  const componentsPropsWithoutEventHandlers = omitEventHandlers(externalSlotProps);\n  const otherPropsWithoutEventHandlers = omitEventHandlers(externalForwardedProps);\n  const internalSlotProps = getSlotProps(eventHandlers);\n\n  // The order of classes is important here.\n  // Emotion (that we use in libraries consuming MUI Base) depends on this order\n  // to properly override style. It requires the most important classes to be last\n  // (see https://github.com/mui/material-ui/pull/33205) for the related discussion.\n  const joinedClasses = clsx(internalSlotProps == null ? void 0 : internalSlotProps.className, additionalProps == null ? void 0 : additionalProps.className, className, externalForwardedProps == null ? void 0 : externalForwardedProps.className, externalSlotProps == null ? void 0 : externalSlotProps.className);\n  const mergedStyle = _extends({}, internalSlotProps == null ? void 0 : internalSlotProps.style, additionalProps == null ? void 0 : additionalProps.style, externalForwardedProps == null ? void 0 : externalForwardedProps.style, externalSlotProps == null ? void 0 : externalSlotProps.style);\n  const props = _extends({}, internalSlotProps, additionalProps, otherPropsWithoutEventHandlers, componentsPropsWithoutEventHandlers);\n  if (joinedClasses.length > 0) {\n    props.className = joinedClasses;\n  }\n  if (Object.keys(mergedStyle).length > 0) {\n    props.style = mergedStyle;\n  }\n  return {\n    props,\n    internalRef: internalSlotProps.ref\n  };\n}", "/**\n * Extracts event handlers from a given object.\n * A prop is considered an event handler if it is a function and its name starts with `on`.\n *\n * @param object An object to extract event handlers from.\n * @param excludeKeys An array of keys to exclude from the returned object.\n */\nexport default function extractEventHandlers(object, excludeKeys = []) {\n  if (object === undefined) {\n    return {};\n  }\n  const result = {};\n  Object.keys(object).filter(prop => prop.match(/^on[A-Z]/) && typeof object[prop] === 'function' && !excludeKeys.includes(prop)).forEach(prop => {\n    result[prop] = object[prop];\n  });\n  return result;\n}", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"elementType\", \"externalSlotProps\", \"ownerState\"];\nimport { unstable_useForkRef as useForkRef } from '@mui/utils';\nimport appendOwnerState from './appendOwnerState';\nimport mergeSlotProps from './mergeSlotProps';\nimport resolveComponentProps from './resolveComponentProps';\n/**\n * Builds the props to be passed into the slot of an unstyled component.\n * It merges the internal props of the component with the ones supplied by the user, allowing to customize the behavior.\n * If the slot component is not a host component, it also merges in the `ownerState`.\n *\n * @param parameters.getSlotProps - A function that returns the props to be passed to the slot component.\n */\nexport default function useSlotProps(parameters) {\n  var _parameters$additiona;\n  const {\n      elementType,\n      externalSlotProps,\n      ownerState\n    } = parameters,\n    rest = _objectWithoutPropertiesLoose(parameters, _excluded);\n  const resolvedComponentsProps = resolveComponentProps(externalSlotProps, ownerState);\n  const {\n    props: mergedProps,\n    internalRef\n  } = mergeSlotProps(_extends({}, rest, {\n    externalSlotProps: resolvedComponentsProps\n  }));\n  const ref = useForkRef(internalRef, resolvedComponentsProps == null ? void 0 : resolvedComponentsProps.ref, (_parameters$additiona = parameters.additionalProps) == null ? void 0 : _parameters$additiona.ref);\n  const props = appendOwnerState(elementType, _extends({}, mergedProps, {\n    ref\n  }), ownerState);\n  return props;\n}", "// Inspired by https://github.com/material-components/material-components-ios/blob/bca36107405594d5b7b16265a5b0ed698f85a5ee/components/Elevation/src/UIColor%2BMaterialElevation.m#L61\nconst getOverlayAlpha = elevation => {\n  let alphaValue;\n  if (elevation < 1) {\n    alphaValue = 5.11916 * elevation ** 2;\n  } else {\n    alphaValue = 4.5 * Math.log(elevation + 1) + 2;\n  }\n  return (alphaValue / 100).toFixed(2);\n};\nexport default getOverlayAlpha;", "import { unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nimport generateUtilityClass from '../generateUtilityClass';\nexport function getPaperUtilityClass(slot) {\n  return generateUtilityClass('MuiPaper', slot);\n}\nconst paperClasses = generateUtilityClasses('MuiPaper', ['root', 'rounded', 'outlined', 'elevation', 'elevation0', 'elevation1', 'elevation2', 'elevation3', 'elevation4', 'elevation5', 'elevation6', 'elevation7', 'elevation8', 'elevation9', 'elevation10', 'elevation11', 'elevation12', 'elevation13', 'elevation14', 'elevation15', 'elevation16', 'elevation17', 'elevation18', 'elevation19', 'elevation20', 'elevation21', 'elevation22', 'elevation23', 'elevation24']);\nexport default paperClasses;", "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"component\", \"elevation\", \"square\", \"variant\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { chainPropTypes, integerPropType } from '@mui/utils';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport { alpha } from '@mui/system';\nimport styled from '../styles/styled';\nimport getOverlayAlpha from '../styles/getOverlayAlpha';\nimport useThemeProps from '../styles/useThemeProps';\nimport useTheme from '../styles/useTheme';\nimport { getPaperUtilityClass } from './paperClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    square,\n    elevation,\n    variant,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', variant, !square && 'rounded', variant === 'elevation' && `elevation${elevation}`]\n  };\n  return composeClasses(slots, getPaperUtilityClass, classes);\n};\nconst PaperRoot = styled('div', {\n  name: 'MuiPaper',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[ownerState.variant], !ownerState.square && styles.rounded, ownerState.variant === 'elevation' && styles[`elevation${ownerState.elevation}`]];\n  }\n})(({\n  theme,\n  ownerState\n}) => {\n  var _theme$vars$overlays;\n  return _extends({\n    backgroundColor: (theme.vars || theme).palette.background.paper,\n    color: (theme.vars || theme).palette.text.primary,\n    transition: theme.transitions.create('box-shadow')\n  }, !ownerState.square && {\n    borderRadius: theme.shape.borderRadius\n  }, ownerState.variant === 'outlined' && {\n    border: `1px solid ${(theme.vars || theme).palette.divider}`\n  }, ownerState.variant === 'elevation' && _extends({\n    boxShadow: (theme.vars || theme).shadows[ownerState.elevation]\n  }, !theme.vars && theme.palette.mode === 'dark' && {\n    backgroundImage: `linear-gradient(${alpha('#fff', getOverlayAlpha(ownerState.elevation))}, ${alpha('#fff', getOverlayAlpha(ownerState.elevation))})`\n  }, theme.vars && {\n    backgroundImage: (_theme$vars$overlays = theme.vars.overlays) == null ? void 0 : _theme$vars$overlays[ownerState.elevation]\n  }));\n});\nconst Paper = /*#__PURE__*/React.forwardRef(function Paper(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiPaper'\n  });\n  const {\n      className,\n      component = 'div',\n      elevation = 1,\n      square = false,\n      variant = 'elevation'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    component,\n    elevation,\n    square,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    const theme = useTheme();\n    if (theme.shadows[elevation] === undefined) {\n      console.error([`MUI: The elevation provided <Paper elevation={${elevation}}> is not available in the theme.`, `Please make sure that \\`theme.shadows[${elevation}]\\` is defined.`].join('\\n'));\n    }\n  }\n  return /*#__PURE__*/_jsx(PaperRoot, _extends({\n    as: component,\n    ownerState: ownerState,\n    className: clsx(classes.root, className),\n    ref: ref\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? Paper.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Shadow depth, corresponds to `dp` in the spec.\n   * It accepts values between 0 and 24 inclusive.\n   * @default 1\n   */\n  elevation: chainPropTypes(integerPropType, props => {\n    const {\n      elevation,\n      variant\n    } = props;\n    if (elevation > 0 && variant === 'outlined') {\n      return new Error(`MUI: Combining \\`elevation={${elevation}}\\` with \\`variant=\"${variant}\"\\` has no effect. Either use \\`elevation={0}\\` or use a different \\`variant\\`.`);\n    }\n    return null;\n  }),\n  /**\n   * If `true`, rounded corners are disabled.\n   * @default false\n   */\n  square: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The variant to use.\n   * @default 'elevation'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['elevation', 'outlined']), PropTypes.string])\n} : void 0;\nexport default Paper;", "import { unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nimport generateUtilityClass from '../generateUtilityClass';\nexport function getBackdropUtilityClass(slot) {\n  return generateUtilityClass('MuiBackdrop', slot);\n}\nconst backdropClasses = generateUtilityClasses('MuiBackdrop', ['root', 'invisible']);\nexport default backdropClasses;", "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"component\", \"components\", \"componentsProps\", \"className\", \"invisible\", \"open\", \"slotProps\", \"slots\", \"transitionDuration\", \"TransitionComponent\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport styled from '../styles/styled';\nimport useThemeProps from '../styles/useThemeProps';\nimport Fade from '../Fade';\nimport { getBackdropUtilityClass } from './backdropClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    invisible\n  } = ownerState;\n  const slots = {\n    root: ['root', invisible && 'invisible']\n  };\n  return composeClasses(slots, getBackdropUtilityClass, classes);\n};\nconst BackdropRoot = styled('div', {\n  name: 'MuiBackdrop',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.invisible && styles.invisible];\n  }\n})(({\n  ownerState\n}) => _extends({\n  position: 'fixed',\n  display: 'flex',\n  alignItems: 'center',\n  justifyContent: 'center',\n  right: 0,\n  bottom: 0,\n  top: 0,\n  left: 0,\n  backgroundColor: 'rgba(0, 0, 0, 0.5)',\n  WebkitTapHighlightColor: 'transparent'\n}, ownerState.invisible && {\n  backgroundColor: 'transparent'\n}));\nconst Backdrop = /*#__PURE__*/React.forwardRef(function Backdrop(inProps, ref) {\n  var _slotProps$root, _ref, _slots$root;\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiBackdrop'\n  });\n  const {\n      children,\n      component = 'div',\n      components = {},\n      componentsProps = {},\n      className,\n      invisible = false,\n      open,\n      slotProps = {},\n      slots = {},\n      transitionDuration,\n      // eslint-disable-next-line react/prop-types\n      TransitionComponent = Fade\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    component,\n    invisible\n  });\n  const classes = useUtilityClasses(ownerState);\n  const rootSlotProps = (_slotProps$root = slotProps.root) != null ? _slotProps$root : componentsProps.root;\n  return /*#__PURE__*/_jsx(TransitionComponent, _extends({\n    in: open,\n    timeout: transitionDuration\n  }, other, {\n    children: /*#__PURE__*/_jsx(BackdropRoot, _extends({\n      \"aria-hidden\": true\n    }, rootSlotProps, {\n      as: (_ref = (_slots$root = slots.root) != null ? _slots$root : components.Root) != null ? _ref : component,\n      className: clsx(classes.root, className, rootSlotProps == null ? void 0 : rootSlotProps.className),\n      ownerState: _extends({}, ownerState, rootSlotProps == null ? void 0 : rootSlotProps.ownerState),\n      classes: classes,\n      ref: ref,\n      children: children\n    }))\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Backdrop.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside.\n   *\n   * This prop is an alias for the `slots` prop.\n   * It's recommended to use the `slots` prop instead.\n   *\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `slotProps` prop.\n   * It's recommended to use the `slotProps` prop instead, as `componentsProps` will be deprecated in the future.\n   *\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    root: PropTypes.object\n  }),\n  /**\n   * If `true`, the backdrop is invisible.\n   * It can be used when rendering a popover or a custom select component.\n   * @default false\n   */\n  invisible: PropTypes.bool,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool.isRequired,\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `componentsProps` prop, which will be deprecated in the future.\n   *\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.object\n  }),\n  /**\n   * The components used for each slot inside.\n   *\n   * This prop is an alias for the `components` prop, which will be deprecated in the future.\n   *\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The duration for the transition, in milliseconds.\n   * You may specify a single timeout for all transitions, or individually with an object.\n   */\n  transitionDuration: PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })])\n} : void 0;\nexport default Backdrop;"], "sourceRoot": ""}