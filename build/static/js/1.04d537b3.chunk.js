(this.webpackJsonpclient=this.webpackJsonpclient||[]).push([[1],{1154:function(e,t,n){"use strict";function o(e,t){return"function"===typeof e?e(t):e}n.d(t,"a",(function(){return o}))},1155:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var o=n(3),r=n(1153);function a(e,t,n){return void 0===e||Object(r.a)(e)?t:Object(o.a)({},t,{ownerState:Object(o.a)({},t.ownerState,n)})}},1281:function(e,t,n){"use strict";var o=n(3),r=n(12),a=n(0),i=n(527),s=n(120),c=n(86),l=n(229),u=n(2);const d=["addEndListener","appear","children","easing","in","onEnter","onEntered","onEntering","onExit","onExited","onExiting","style","timeout","TransitionComponent"],b={entering:{opacity:1},entered:{opacity:1}},p=a.forwardRef((function(e,t){const n=Object(s.a)(),p={enter:n.transitions.duration.enteringScreen,exit:n.transitions.duration.leavingScreen},{addEndListener:f,appear:v=!0,children:m,easing:h,in:O,onEnter:j,onEntered:y,onEntering:E,onExit:g,onExited:x,onExiting:k,style:R,timeout:w=p,TransitionComponent:P=i.a}=e,S=Object(r.a)(e,d),T=a.useRef(null),N=Object(l.a)(T,m.ref,t),A=e=>t=>{if(e){const n=T.current;void 0===t?e(n):e(n,t)}},F=A(E),M=A(((e,t)=>{Object(c.b)(e);const o=Object(c.a)({style:R,timeout:w,easing:h},{mode:"enter"});e.style.webkitTransition=n.transitions.create("opacity",o),e.style.transition=n.transitions.create("opacity",o),j&&j(e,t)})),I=A(y),C=A(k),L=A((e=>{const t=Object(c.a)({style:R,timeout:w,easing:h},{mode:"exit"});e.style.webkitTransition=n.transitions.create("opacity",t),e.style.transition=n.transitions.create("opacity",t),g&&g(e)})),B=A(x);return Object(u.jsx)(P,Object(o.a)({appear:v,in:O,nodeRef:T,onEnter:M,onEntered:I,onEntering:F,onExit:L,onExited:B,onExiting:C,addEndListener:e=>{f&&f(T.current,e)},timeout:w},S,{children:(e,t)=>a.cloneElement(m,Object(o.a)({style:Object(o.a)({opacity:0,visibility:"exited"!==e||O?void 0:"hidden"},b[e],R,m.props.style),ref:N},t))}))}));t.a=p},1282:function(e,t,n){"use strict";function o(e){const t=e.documentElement.clientWidth;return Math.abs(window.innerWidth-t)}n.d(t,"a",(function(){return o}))},1283:function(e,t,n){"use strict";function o(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.reduce(((e,t)=>null==t?e:function(){for(var n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];e.apply(this,o),t.apply(this,o)}),(()=>{}))}n.d(t,"a",(function(){return o}))},1284:function(e,t,n){"use strict";var o=n(0),r=n(50),a=n(338),i=n(218),s=n(330),c=n(2);const l=o.forwardRef((function(e,t){const{children:n,container:l,disablePortal:u=!1}=e,[d,b]=o.useState(null),p=Object(a.a)(o.isValidElement(n)?n.ref:null,t);if(Object(i.a)((()=>{u||b(function(e){return"function"===typeof e?e():e}(l)||document.body)}),[l,u]),Object(i.a)((()=>{if(d&&!u)return Object(s.a)(t,d),()=>{Object(s.a)(t,null)}}),[t,d,u]),u){if(o.isValidElement(n)){const e={ref:p};return o.cloneElement(n,e)}return Object(c.jsx)(o.Fragment,{children:n})}return Object(c.jsx)(o.Fragment,{children:d?r.createPortal(n,d):d})}));t.a=l},1285:function(e,t,n){"use strict";var o=n(0),r=n(338),a=n(136),i=n(2);const s=["input","select","textarea","a[href]","button","[tabindex]","audio[controls]","video[controls]",'[contenteditable]:not([contenteditable="false"])'].join(",");function c(e){const t=[],n=[];return Array.from(e.querySelectorAll(s)).forEach(((e,o)=>{const r=function(e){const t=parseInt(e.getAttribute("tabindex")||"",10);return Number.isNaN(t)?"true"===e.contentEditable||("AUDIO"===e.nodeName||"VIDEO"===e.nodeName||"DETAILS"===e.nodeName)&&null===e.getAttribute("tabindex")?0:e.tabIndex:t}(e);-1!==r&&function(e){return!(e.disabled||"INPUT"===e.tagName&&"hidden"===e.type||function(e){if("INPUT"!==e.tagName||"radio"!==e.type)return!1;if(!e.name)return!1;const t=t=>e.ownerDocument.querySelector('input[type="radio"]'.concat(t));let n=t('[name="'.concat(e.name,'"]:checked'));return n||(n=t('[name="'.concat(e.name,'"]'))),n!==e}(e))}(e)&&(0===r?t.push(e):n.push({documentOrder:o,tabIndex:r,node:e}))})),n.sort(((e,t)=>e.tabIndex===t.tabIndex?e.documentOrder-t.documentOrder:e.tabIndex-t.tabIndex)).map((e=>e.node)).concat(t)}function l(){return!0}t.a=function(e){const{children:t,disableAutoFocus:n=!1,disableEnforceFocus:s=!1,disableRestoreFocus:u=!1,getTabbable:d=c,isEnabled:b=l,open:p}=e,f=o.useRef(!1),v=o.useRef(null),m=o.useRef(null),h=o.useRef(null),O=o.useRef(null),j=o.useRef(!1),y=o.useRef(null),E=Object(r.a)(t.ref,y),g=o.useRef(null);o.useEffect((()=>{p&&y.current&&(j.current=!n)}),[n,p]),o.useEffect((()=>{if(!p||!y.current)return;const e=Object(a.a)(y.current);return y.current.contains(e.activeElement)||(y.current.hasAttribute("tabIndex")||y.current.setAttribute("tabIndex","-1"),j.current&&y.current.focus()),()=>{u||(h.current&&h.current.focus&&(f.current=!0,h.current.focus()),h.current=null)}}),[p]),o.useEffect((()=>{if(!p||!y.current)return;const e=Object(a.a)(y.current),t=t=>{const{current:n}=y;if(null!==n)if(e.hasFocus()&&!s&&b()&&!f.current){if(!n.contains(e.activeElement)){if(t&&O.current!==t.target||e.activeElement!==O.current)O.current=null;else if(null!==O.current)return;if(!j.current)return;let a=[];if(e.activeElement!==v.current&&e.activeElement!==m.current||(a=d(y.current)),a.length>0){var o,r;const e=Boolean((null==(o=g.current)?void 0:o.shiftKey)&&"Tab"===(null==(r=g.current)?void 0:r.key)),t=a[0],n=a[a.length-1];"string"!==typeof t&&"string"!==typeof n&&(e?n.focus():t.focus())}else n.focus()}}else f.current=!1},n=t=>{g.current=t,!s&&b()&&"Tab"===t.key&&e.activeElement===y.current&&t.shiftKey&&(f.current=!0,m.current&&m.current.focus())};e.addEventListener("focusin",t),e.addEventListener("keydown",n,!0);const o=setInterval((()=>{e.activeElement&&"BODY"===e.activeElement.tagName&&t(null)}),50);return()=>{clearInterval(o),e.removeEventListener("focusin",t),e.removeEventListener("keydown",n,!0)}}),[n,s,u,b,p,d]);const x=e=>{null===h.current&&(h.current=e.relatedTarget),j.current=!0};return Object(i.jsxs)(o.Fragment,{children:[Object(i.jsx)("div",{tabIndex:p?0:-1,onFocus:x,ref:v,"data-testid":"sentinelStart"}),o.cloneElement(t,{ref:E,onFocus:e=>{null===h.current&&(h.current=e.relatedTarget),j.current=!0,O.current=e.target;const n=t.props.onFocus;n&&n(e)}}),Object(i.jsx)("div",{tabIndex:p?0:-1,onFocus:x,ref:m,"data-testid":"sentinelEnd"})]})}},1316:function(e,t,n){"use strict";var o=n(12),r=n(3),a=n(0),i=n(542),s=n(516);function c(e){return Object(s.a)("MuiModal",e)}Object(i.a)("MuiModal",["root","hidden"]);var l=n(338),u=n(136),d=n(518),b=n(1283),p=n(541),f=n(1284),v=n(514),m=n(1282);function h(e,t){t?e.setAttribute("aria-hidden","true"):e.removeAttribute("aria-hidden")}function O(e){return parseInt(Object(v.a)(e).getComputedStyle(e).paddingRight,10)||0}function j(e,t,n,o,r){const a=[t,n,...o];[].forEach.call(e.children,(e=>{const t=-1===a.indexOf(e),n=!function(e){const t=-1!==["TEMPLATE","SCRIPT","STYLE","LINK","MAP","META","NOSCRIPT","PICTURE","COL","COLGROUP","PARAM","SLOT","SOURCE","TRACK"].indexOf(e.tagName),n="INPUT"===e.tagName&&"hidden"===e.getAttribute("type");return t||n}(e);t&&n&&h(e,r)}))}function y(e,t){let n=-1;return e.some(((e,o)=>!!t(e)&&(n=o,!0))),n}function E(e,t){const n=[],o=e.container;if(!t.disableScrollLock){if(function(e){const t=Object(u.a)(e);return t.body===e?Object(v.a)(e).innerWidth>t.documentElement.clientWidth:e.scrollHeight>e.clientHeight}(o)){const e=Object(m.a)(Object(u.a)(o));n.push({value:o.style.paddingRight,property:"padding-right",el:o}),o.style.paddingRight="".concat(O(o)+e,"px");const t=Object(u.a)(o).querySelectorAll(".mui-fixed");[].forEach.call(t,(t=>{n.push({value:t.style.paddingRight,property:"padding-right",el:t}),t.style.paddingRight="".concat(O(t)+e,"px")}))}let e;if(o.parentNode instanceof DocumentFragment)e=Object(u.a)(o).body;else{const t=o.parentElement,n=Object(v.a)(o);e="HTML"===(null==t?void 0:t.nodeName)&&"scroll"===n.getComputedStyle(t).overflowY?t:o}n.push({value:e.style.overflow,property:"overflow",el:e},{value:e.style.overflowX,property:"overflow-x",el:e},{value:e.style.overflowY,property:"overflow-y",el:e}),e.style.overflow="hidden"}return()=>{n.forEach((e=>{let{value:t,el:n,property:o}=e;t?n.style.setProperty(o,t):n.style.removeProperty(o)}))}}var g=n(1285),x=n(1317),k=n(2);const R=["children","classes","closeAfterTransition","component","container","disableAutoFocus","disableEnforceFocus","disableEscapeKeyDown","disablePortal","disableRestoreFocus","disableScrollLock","hideBackdrop","keepMounted","manager","onBackdropClick","onClose","onKeyDown","open","onTransitionEnter","onTransitionExited","slotProps","slots"];const w=new class{constructor(){this.containers=void 0,this.modals=void 0,this.modals=[],this.containers=[]}add(e,t){let n=this.modals.indexOf(e);if(-1!==n)return n;n=this.modals.length,this.modals.push(e),e.modalRef&&h(e.modalRef,!1);const o=function(e){const t=[];return[].forEach.call(e.children,(e=>{"true"===e.getAttribute("aria-hidden")&&t.push(e)})),t}(t);j(t,e.mount,e.modalRef,o,!0);const r=y(this.containers,(e=>e.container===t));return-1!==r?(this.containers[r].modals.push(e),n):(this.containers.push({modals:[e],container:t,restore:null,hiddenSiblings:o}),n)}mount(e,t){const n=y(this.containers,(t=>-1!==t.modals.indexOf(e))),o=this.containers[n];o.restore||(o.restore=E(o,t))}remove(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];const n=this.modals.indexOf(e);if(-1===n)return n;const o=y(this.containers,(t=>-1!==t.modals.indexOf(e))),r=this.containers[o];if(r.modals.splice(r.modals.indexOf(e),1),this.modals.splice(n,1),0===r.modals.length)r.restore&&r.restore(),e.modalRef&&h(e.modalRef,t),j(r.container,e.mount,e.modalRef,r.hiddenSiblings,!1),this.containers.splice(o,1);else{const e=r.modals[r.modals.length-1];e.modalRef&&h(e.modalRef,!1)}return n}isTopModal(e){return this.modals.length>0&&this.modals[this.modals.length-1]===e}};var P=a.forwardRef((function(e,t){var n,i;const{children:s,classes:v,closeAfterTransition:m=!1,component:O,container:j,disableAutoFocus:y=!1,disableEnforceFocus:E=!1,disableEscapeKeyDown:P=!1,disablePortal:S=!1,disableRestoreFocus:T=!1,disableScrollLock:N=!1,hideBackdrop:A=!1,keepMounted:F=!1,manager:M=w,onBackdropClick:I,onClose:C,onKeyDown:L,open:B,onTransitionEnter:D,onTransitionExited:K,slotProps:q={},slots:U={}}=e,W=Object(o.a)(e,R),[H,Y]=a.useState(!B),z=a.useRef({}),V=a.useRef(null),J=a.useRef(null),Z=Object(l.a)(J,t),G=function(e){return!!e&&e.props.hasOwnProperty("in")}(s),X=null==(n=e["aria-hidden"])||n,Q=()=>(z.current.modalRef=J.current,z.current.mountNode=V.current,z.current),$=()=>{M.mount(Q(),{disableScrollLock:N}),J.current&&(J.current.scrollTop=0)},_=Object(d.a)((()=>{const e=function(e){return"function"===typeof e?e():e}(j)||Object(u.a)(V.current).body;M.add(Q(),e),J.current&&$()})),ee=a.useCallback((()=>M.isTopModal(Q())),[M]),te=Object(d.a)((e=>{V.current=e,e&&J.current&&(B&&ee()?$():h(J.current,X))})),ne=a.useCallback((()=>{M.remove(Q(),X)}),[M,X]);a.useEffect((()=>()=>{ne()}),[ne]),a.useEffect((()=>{B?_():G&&m||ne()}),[B,ne,G,m,_]);const oe=Object(r.a)({},e,{classes:v,closeAfterTransition:m,disableAutoFocus:y,disableEnforceFocus:E,disableEscapeKeyDown:P,disablePortal:S,disableRestoreFocus:T,disableScrollLock:N,exited:H,hideBackdrop:A,keepMounted:F}),re=(e=>{const{open:t,exited:n,classes:o}=e,r={root:["root",!t&&n&&"hidden"],backdrop:["backdrop"]};return Object(p.a)(r,c,o)})(oe),ae=()=>{Y(!1),D&&D()},ie=()=>{Y(!0),K&&K(),m&&ne()},se={};void 0===s.props.tabIndex&&(se.tabIndex="-1"),G&&(se.onEnter=Object(b.a)(ae,s.props.onEnter),se.onExited=Object(b.a)(ie,s.props.onExited));const ce=null!=(i=null!=O?O:U.root)?i:"div",le=Object(x.a)({elementType:ce,externalSlotProps:q.root,externalForwardedProps:W,additionalProps:{ref:Z,role:"presentation",onKeyDown:e=>{L&&L(e),"Escape"===e.key&&ee()&&(P||(e.stopPropagation(),C&&C(e,"escapeKeyDown")))}},className:re.root,ownerState:oe}),ue=U.backdrop,de=Object(x.a)({elementType:ue,externalSlotProps:q.backdrop,additionalProps:{"aria-hidden":!0,onClick:e=>{e.target===e.currentTarget&&(I&&I(e),C&&C(e,"backdropClick"))},open:B},className:re.backdrop,ownerState:oe});return F||B||G&&!H?Object(k.jsx)(f.a,{ref:te,container:j,disablePortal:S,children:Object(k.jsxs)(ce,Object(r.a)({},le,{children:[!A&&ue?Object(k.jsx)(ue,Object(r.a)({},de)):null,Object(k.jsx)(g.a,{disableEnforceFocus:E,disableAutoFocus:y,disableRestoreFocus:T,isEnabled:ee,open:B,children:a.cloneElement(s,se)})]}))}):null})),S=n(1154),T=n(1153),N=n(47),A=n(67),F=n(1331);const M=["BackdropComponent","BackdropProps","closeAfterTransition","children","component","components","componentsProps","disableAutoFocus","disableEnforceFocus","disableEscapeKeyDown","disablePortal","disableRestoreFocus","disableScrollLock","hideBackdrop","keepMounted","slotProps","slots","theme"],I=Object(N.a)("div",{name:"MuiModal",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,!n.open&&n.exited&&t.hidden]}})((e=>{let{theme:t,ownerState:n}=e;return Object(r.a)({position:"fixed",zIndex:(t.vars||t).zIndex.modal,right:0,bottom:0,top:0,left:0},!n.open&&n.exited&&{visibility:"hidden"})})),C=Object(N.a)(F.a,{name:"MuiModal",slot:"Backdrop",overridesResolver:(e,t)=>t.backdrop})({zIndex:-1}),L=a.forwardRef((function(e,t){var n,i,s,c,l,u;const d=Object(A.a)({name:"MuiModal",props:e}),{BackdropComponent:b=C,BackdropProps:p,closeAfterTransition:f=!1,children:v,component:m,components:h={},componentsProps:O={},disableAutoFocus:j=!1,disableEnforceFocus:y=!1,disableEscapeKeyDown:E=!1,disablePortal:g=!1,disableRestoreFocus:x=!1,disableScrollLock:R=!1,hideBackdrop:w=!1,keepMounted:N=!1,slotProps:F,slots:L,theme:B}=d,D=Object(o.a)(d,M),[K,q]=a.useState(!0),U={closeAfterTransition:f,disableAutoFocus:j,disableEnforceFocus:y,disableEscapeKeyDown:E,disablePortal:g,disableRestoreFocus:x,disableScrollLock:R,hideBackdrop:w,keepMounted:N},W=Object(r.a)({},d,U,{exited:K}),H=(e=>e.classes)(W),Y=null!=(n=null!=(i=null==L?void 0:L.root)?i:h.Root)?n:I,z=null!=(s=null!=(c=null==L?void 0:L.backdrop)?c:h.Backdrop)?s:b,V=null!=(l=null==F?void 0:F.root)?l:O.root,J=null!=(u=null==F?void 0:F.backdrop)?u:O.backdrop;return Object(k.jsx)(P,Object(r.a)({slots:{root:Y,backdrop:z},slotProps:{root:()=>Object(r.a)({},Object(S.a)(V,W),!Object(T.a)(Y)&&{as:m,theme:B}),backdrop:()=>Object(r.a)({},p,Object(S.a)(J,W))},onTransitionEnter:()=>q(!1),onTransitionExited:()=>q(!0),ref:t},D,{classes:H},U,{children:v}))}));t.a=L},1317:function(e,t,n){"use strict";n.d(t,"a",(function(){return b}));var o=n(3),r=n(12),a=n(338),i=n(1155),s=n(31);function c(e){if(void 0===e)return{};const t={};return Object.keys(e).filter((t=>!(t.match(/^on[A-Z]/)&&"function"===typeof e[t]))).forEach((n=>{t[n]=e[n]})),t}function l(e){const{getSlotProps:t,additionalProps:n,externalSlotProps:r,externalForwardedProps:a,className:i}=e;if(!t){const e=Object(s.a)(null==a?void 0:a.className,null==r?void 0:r.className,i,null==n?void 0:n.className),t=Object(o.a)({},null==n?void 0:n.style,null==a?void 0:a.style,null==r?void 0:r.style),c=Object(o.a)({},n,a,r);return e.length>0&&(c.className=e),Object.keys(t).length>0&&(c.style=t),{props:c,internalRef:void 0}}const l=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];if(void 0===e)return{};const n={};return Object.keys(e).filter((n=>n.match(/^on[A-Z]/)&&"function"===typeof e[n]&&!t.includes(n))).forEach((t=>{n[t]=e[t]})),n}(Object(o.a)({},a,r)),u=c(r),d=c(a),b=t(l),p=Object(s.a)(null==b?void 0:b.className,null==n?void 0:n.className,i,null==a?void 0:a.className,null==r?void 0:r.className),f=Object(o.a)({},null==b?void 0:b.style,null==n?void 0:n.style,null==a?void 0:a.style,null==r?void 0:r.style),v=Object(o.a)({},b,n,d,u);return p.length>0&&(v.className=p),Object.keys(f).length>0&&(v.style=f),{props:v,internalRef:b.ref}}var u=n(1154);const d=["elementType","externalSlotProps","ownerState"];function b(e){var t;const{elementType:n,externalSlotProps:s,ownerState:c}=e,b=Object(r.a)(e,d),p=Object(u.a)(s,c),{props:f,internalRef:v}=l(Object(o.a)({},b,{externalSlotProps:p})),m=Object(a.a)(v,null==p?void 0:p.ref,null==(t=e.additionalProps)?void 0:t.ref);return Object(i.a)(n,Object(o.a)({},f,{ref:m}),c)}},1319:function(e,t,n){"use strict";var o=n(12),r=n(3),a=n(0),i=n(31),s=n(541),c=n(539),l=n(47);var u=e=>{let t;return t=e<1?5.11916*e**2:4.5*Math.log(e+1)+2,(t/100).toFixed(2)},d=n(67),b=n(542),p=n(516);function f(e){return Object(p.a)("MuiPaper",e)}Object(b.a)("MuiPaper",["root","rounded","outlined","elevation","elevation0","elevation1","elevation2","elevation3","elevation4","elevation5","elevation6","elevation7","elevation8","elevation9","elevation10","elevation11","elevation12","elevation13","elevation14","elevation15","elevation16","elevation17","elevation18","elevation19","elevation20","elevation21","elevation22","elevation23","elevation24"]);var v=n(2);const m=["className","component","elevation","square","variant"],h=Object(l.a)("div",{name:"MuiPaper",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,t[n.variant],!n.square&&t.rounded,"elevation"===n.variant&&t["elevation".concat(n.elevation)]]}})((e=>{let{theme:t,ownerState:n}=e;var o;return Object(r.a)({backgroundColor:(t.vars||t).palette.background.paper,color:(t.vars||t).palette.text.primary,transition:t.transitions.create("box-shadow")},!n.square&&{borderRadius:t.shape.borderRadius},"outlined"===n.variant&&{border:"1px solid ".concat((t.vars||t).palette.divider)},"elevation"===n.variant&&Object(r.a)({boxShadow:(t.vars||t).shadows[n.elevation]},!t.vars&&"dark"===t.palette.mode&&{backgroundImage:"linear-gradient(".concat(Object(c.a)("#fff",u(n.elevation)),", ").concat(Object(c.a)("#fff",u(n.elevation)),")")},t.vars&&{backgroundImage:null==(o=t.vars.overlays)?void 0:o[n.elevation]}))})),O=a.forwardRef((function(e,t){const n=Object(d.a)({props:e,name:"MuiPaper"}),{className:a,component:c="div",elevation:l=1,square:u=!1,variant:b="elevation"}=n,p=Object(o.a)(n,m),O=Object(r.a)({},n,{component:c,elevation:l,square:u,variant:b}),j=(e=>{const{square:t,elevation:n,variant:o,classes:r}=e,a={root:["root",o,!t&&"rounded","elevation"===o&&"elevation".concat(n)]};return Object(s.a)(a,f,r)})(O);return Object(v.jsx)(h,Object(r.a)({as:c,ownerState:O,className:Object(i.a)(j.root,a),ref:t},p))}));t.a=O},1331:function(e,t,n){"use strict";var o=n(12),r=n(3),a=n(0),i=n(31),s=n(541),c=n(47),l=n(67),u=n(1281),d=n(542),b=n(516);function p(e){return Object(b.a)("MuiBackdrop",e)}Object(d.a)("MuiBackdrop",["root","invisible"]);var f=n(2);const v=["children","component","components","componentsProps","className","invisible","open","slotProps","slots","transitionDuration","TransitionComponent"],m=Object(c.a)("div",{name:"MuiBackdrop",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,n.invisible&&t.invisible]}})((e=>{let{ownerState:t}=e;return Object(r.a)({position:"fixed",display:"flex",alignItems:"center",justifyContent:"center",right:0,bottom:0,top:0,left:0,backgroundColor:"rgba(0, 0, 0, 0.5)",WebkitTapHighlightColor:"transparent"},t.invisible&&{backgroundColor:"transparent"})})),h=a.forwardRef((function(e,t){var n,a,c;const d=Object(l.a)({props:e,name:"MuiBackdrop"}),{children:b,component:h="div",components:O={},componentsProps:j={},className:y,invisible:E=!1,open:g,slotProps:x={},slots:k={},transitionDuration:R,TransitionComponent:w=u.a}=d,P=Object(o.a)(d,v),S=Object(r.a)({},d,{component:h,invisible:E}),T=(e=>{const{classes:t,invisible:n}=e,o={root:["root",n&&"invisible"]};return Object(s.a)(o,p,t)})(S),N=null!=(n=x.root)?n:j.root;return Object(f.jsx)(w,Object(r.a)({in:g,timeout:R},P,{children:Object(f.jsx)(m,Object(r.a)({"aria-hidden":!0},N,{as:null!=(a=null!=(c=k.root)?c:O.Root)?a:h,className:Object(i.a)(T.root,y,null==N?void 0:N.className),ownerState:Object(r.a)({},S,null==N?void 0:N.ownerState),classes:T,ref:t,children:b}))}))}));t.a=h}}]);
//# sourceMappingURL=1.04d537b3.chunk.js.map