const mongoose = require("mongoose");
const Schema = mongoose.Schema;
// Create Schema
const DriverProfileSchema = new Schema({
    phoneNumber: {
        type: String,
        default: ""
    },
    drivername: {
        type: String,
        default: ""
    },
    address: {
        type: String,
        default: "",
    },
    createdAt: {
        type: Date,
        default: Date.now()
    },
    invoiceId: {
        type: String,
        defalut: ''
    },
    realInvoiceId: {
        type: String,
        defalut: ''
    },
    paid: {
        type: Boolean,
        default: false
    },
    image: {
        type: Buffer,
    },

});
module.exports = DriverProfile = mongoose.model("DriverProfile", DriverProfileSchema);