const mongoose = require("mongoose");
const Schema = mongoose.Schema;
// Create Schema
const UserSchema = new Schema({
    phoneNumber: {
        type: String,
    },

    username: {
        type: String,

    },
    pinCode: {
        type: String,
        default: '',
    },
    status: {
        type: String,
        default: "active"
    },
    role: {
        type: String,
        default: 'user',
    },
    licenseKey: {
        type: String,
        default: ''
    },
    expired: {
        type: Date,
        default: new Date((Date.now() + 30 * 24 * 3600 * 1000))
    },
    driverLicenseFile: {
        type: String,
        default: '',
    },
    driverLicenseVerification: {
        type: Number,
        default: 0 // 0: no-license, 1: pending, 2: verified, 3: updated
    },
    address: {
        type: String,
        default: '',
    },
    description: {
        type: String,
        default: '',
    },
    balance: {
        type: Number,
        default: 0,
    },
    fmctoken: {
        type: String,
        default: 0,
    }
});
module.exports = User = mongoose.model("user", UserSchema);