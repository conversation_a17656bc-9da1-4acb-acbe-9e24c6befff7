const mongoose = require("mongoose");
const Schema = mongoose.Schema;
// Create Schema
const BikeSchema = new Schema({
    Bikemac: {
        type: String,
        default: ''
    },
    createdAt: {
        type: Date,
        default: Date.now()
    },
    status: {
        type: String,
        default: "active",
    },
    rentable: {
        type: Boolean,
        default: false,
    },
    rented: {
        type: Boolean,
        default: false,
    },
    fmctoken: {
        type: String,
        default: 0,
    },
    renter: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        default: null
    }
});

module.exports = Device = mongoose.model("bike", BikeSchema);