const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const BikeRentDetailLogSchema = new Schema({
    bike: {
        type: Schema.Types.ObjectId,
        ref: "Bike",
        required: true,
    },
    renter: {
        type: Schema.Types.ObjectId,
        ref: "User",
        required: true,
    },
    status: {
        type: String,
        default: "success",
    },
    command: {
        type: String,
        default: "",
    },
    sendTime: {
        type: Date,
        default: Date.now,
    },
    receiveTime: {
        type: Date,
        default: null,
    },
    response: {
        type: String,
        default: "",
    },
    responseType: {
        type: String,
        default: "",
    },
});

module.exports = mongoose.model("BikeRentDetailLog", BikeRentDetailLogSchema);