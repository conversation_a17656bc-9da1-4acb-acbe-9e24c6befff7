const mongoose = require('mongoose');

const ScheduledCommandSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  deviceNumber: {
    type: String,
    required: true
  },
  topic: {
    type: String,
    required: true
  },
  payload: {
    type: String,
    required: true
  },
  scheduledTime: {
    type: Date,
    required: true
  },
  isRecurring: {
    type: Boolean,
    default: false
  },
  lastExecuted: {
    type: Date,
    default: null
  },
  status: {
    type: String,
    enum: ['pending', 'executed', 'failed', 'skipped'],
    default: 'pending'
  },
  executionResult: {
    type: String,
    default: null
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

module.exports = mongoose.model('ScheduledCommand', ScheduledCommandSchema);
