const mongoose = require("mongoose");
const Schema = mongoose.Schema;
// Create Schema
const MopedRentLogSchema = new Schema({
    deviceNumber: {
        type: String,
        default: ""
    },
    deviceName:{
        type:String,
        default:''
    },
    renter:{
        type: String,
        default:''
    },
    from:{
        type:Number,
        default:0,
    },
    to:{
        type:Number,
        default:0,
    },
    mode:{
        type:String,
        default:'minute'
    },
    created:{
        type:Number,
        default:Date.now()
    }
});

module.exports = MopedRentLog = mongoose.model("MopedRentLog", MopedRentLogSchema);