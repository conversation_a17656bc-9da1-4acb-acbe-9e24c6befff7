const mongoose = require("mongoose");
const Schema = mongoose.Schema;
// Create Schema
const MopedMessageLogSchema = new Schema({
    deviceName:{
        type:String,
        default:''
    },
    deviceNumber: {
        type: String,
        default: ""
    },
    deviceType:{
        type:String,
        default:'4g'
    },
    uix:{
        type:String,
        default:''
    },

    renter: {
        type: String,
        default: ""
    },
    sent:{
        type:Number,
        default:0,
    },
    received:{
        type:Number,
        default:0,
    },
    payloads:{
        type:Array,
    },
    
});
module.exports = mopedMessageLog = mongoose.model("mopedMessageLog", MopedMessageLogSchema);