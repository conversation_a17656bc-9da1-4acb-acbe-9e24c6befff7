const mongoose = require("mongoose");
const Schema = mongoose.Schema;
// Create Schema
const CarRentCheckSchema = new Schema({
     
    deviceNumber: {
        type: String,
        default: ""
    },
     
    command: {
        type: String,
        default: ""
    },
    response: {
        type: String,
        default: "Response Message"
    },
    receiveTime: {
        type: Date,
        default:0,
    },
    message: {
        type: String,
        default: ""
    },
    
}, { capped: { size: 102400, max: 100000, autoIndexId: true } });
module.exports = carRentCheck = mongoose.model("carRentCheck", CarRentCheckSchema);