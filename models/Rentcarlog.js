const mongoose = require("mongoose");
const Schema = mongoose.Schema;
// Create Schema
const rentcarlogSchema = new Schema({
    user: {
        type: String,
    },
    deviceNumber: {
        type: String,
        default: ""
    },
    deviceType: {
        type: String,
        default: '4g',
    },
    command: {
        type: String,
        default: ""
    },
    sent: {
        type: String,
        default: "No"
    },
    response: {
        type: String,
        default: "Response Message"
    },
    sentTime: {
        type: Date,

    },
    receiveTime: {
        type: Date,

    },
    message: {
        type: String,
        default: ""
    },
    responseType: {
        type: String,
        default: "HTTP"
    },
    share: {
        type: String,
        default: "no",
    },
    occupied: {
        type: String,
        default: "no",
    },
    createdAt: {
        type: Date,
        default: Date.now(),
    }
}, { capped: { size: 102400, max: 100000, autoIndexId: true } });
module.exports = rentcarlog = mongoose.model("rentcarlog", rentcarlogSchema);