const activeUsers = new Set();
const user = require('../models/user');
const messagesForward = (deviceNumber, msg, from) => {
    activeUsers.forEach(async(user) => {
        const user_ = await UserModel.findOne({});
        // mtqq data recive
        msg.payload = msg.payload.replace(" N,", ",").replace(" E,", ",").replace(/\s,\s,/g, ",").replace(/\s,,/g, ",").replace("(", "{").replace("\x1B)", "}").replace(" ,,", ",");

    })
}
module.exports = {
    messagesForward
}