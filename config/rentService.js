let rentableCars = [];
const getRentableCarList = () => {

    return rentableCars;
}
const updateRentableCarList = (cars) => {
    rentableCars = cars;
}
const getCarRentPrice = (mode) => {
    const table = getCarRentPriceTable();
    return (mode == 'minute' ? table[0] : (mode == 'hour' ? table[1] : table[2]));
}
const getCarRentPriceTable =()=>{
    const pricePerMinute = parseInt(process.env.CAR_RENT_MINUTE);
    const pricePerHour = parseInt(process.env.CAR_RENT_HOURLY);
    const pricePerDay = parseInt(process.env.CAR_RENT_DAILY);
    return [pricePerMinute,pricePerHour,pricePerDay]
}
module.exports = {
    getRentableCarList,
    updateRentableCarList,
    getCarRentPrice,
    getCarRentPriceTable
}