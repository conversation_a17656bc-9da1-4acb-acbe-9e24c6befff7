const express = require('express');

const router = express.Router();
const auth = require('../../middleware/auth');

const { getGpsLogByDate, getLogByDate, getSmsLog, getLogList, getLogListByAll, getListByAllShare, deleteLog, getSimStatusLog, removeSimLog, readSimLog, getSimLogs, fetchLastDataByDeviceNumber } = require('../../controller/logController');


/*************************** */

router.post('/gps-get-by-date', auth, getGpsLogByDate)
router.post('/get-by-date', auth, getLogByDate)
router.get('/fetch-by-device', auth, fetchLastDataByDeviceNumber)
router.get('/sms-logs/:from/:to', auth, getSmsLog)
router.post('/list', auth, getLogList)
router.get('/list-by-all', auth, getLogListByAll)
router.get('/list-by-all-share', getListByAllShare)
router.post("/delete", auth, deleteLog)
router.get('/sim-status', getSimStatusLog);
router.get('/sim-logs', getSimLogs);

router.delete('/remove-sim-log/:id',auth, removeSimLog);
router.post('/read-sim-log',auth, readSimLog);
module.exports = router;