const express = require("express");
const router = express.Router();
const auth = require("../../middleware/auth");

const authController = require('../../controller/authController');
const userController = require('../../controller/userController');


/************************* */

router.post('/pincode', authController.verifyPincode);
router.post("/login", authController.login);
router.post("/verifyOtp", authController.verifyOtp);
router.post("/register", authController.register);
router.post('/set-pincode', userController.setPincode);
router.post('/set-pin', userController.setPin);

router.post('/request-withdraw', auth, userController.requestWithdraw);

router.post('/set-bank', auth, userController.updateBank);
router.get("/my-account", auth, userController.myAccount);
router.post('/reset-password', userController.resetPassword);
router.post('/update-expiry', auth, userController.updateUserExpiry);
router.post('/savefcmtoken', userController.savefcmtoken);

module.exports = router;
