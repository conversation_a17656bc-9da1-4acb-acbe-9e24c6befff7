const express = require("express");
const router = express.Router();
const path = require('path');

/************ */

router.get('/pki-validation/:fileName', (req, res) => {
    try {
        const fileName = req.params.fileName;
        const options = {
            root: path.join(__dirname)
        }
        res.sendFile(path.resolve(`certs/${fileName}`), {}, (err) => {
            if (err) {
                console.log("err", err);
            }
        })
    } catch (err) {
        console.log(err)
    }

});

module.exports = router;