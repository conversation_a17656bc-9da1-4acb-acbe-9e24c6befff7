const express = require('express');
const router = express.Router();
const controller = require('../../controller/adminController');
const auth = require('../../middleware/auth');
const admin = require('../../middleware/admin');

/****************** */

router.get('/user/list', auth, admin, controller.userList);
router.post('/user/wallet-change', auth, admin, controller.walletChange);
router.get('/wallet-requests', auth, admin, controller.walletRequest);
router.get('/rent-car-status', auth, admin, controller.rentCarStatus);
router.get('/wallet-transactions', auth, admin, controller.walletTransactions);
router.post('/user/delete', auth, admin, controller.removeUsers);
router.post('/user/change-active', auth, admin, controller.changeActive);
router.post('/user/driver-license-verification', auth, admin, controller.driverLicenseVerification);
router.post('/user/extend-license', auth, admin, controller.extendsLicense);
router.get('/order/list', auth, admin, controller.orderList);
router.post('/notification/send',auth,admin,controller.sendNotifications);
module.exports = router;