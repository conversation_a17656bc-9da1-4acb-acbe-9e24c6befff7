const express = require('express');
const router = express.Router();
const Device = require('../../models/device');
const { sendMessageToChannel } = require('../../utils/socket');
const LogModel = require('../../models/log');
const UserModel = require('../../models/user');

/******************* */

// from sms iot, iot device call this function using your sms api return hook url
const parseURLParams = (url) => {
    var queryStart = url.indexOf("?") + 1,
        queryEnd = url.indexOf("#") + 1 || url.length + 1,
        query = url.slice(queryStart, queryEnd - 1),
        pairs = query.replace(/\+/g, " ").split("&"),
        parms = {},
        i, n, v, nv;

    if (query === url || query === "") return;

    for (i = 0; i < pairs.length; i++) {
        nv = pairs[i].split("=", 2);
        n = decodeURIComponent(nv[0]);
        v = decodeURIComponent(nv[1]);

        if (!parms.hasOwnProperty(n)) parms[n] = [];
        parms[n].push(nv.length === 2 ? v : null);
    }
    return parms;
}

router.get('/sms', async (req, res) => {
    try {
        const data = parseURLParams(req.url);
        console.log(data, " is parse data from sms");
        let mobile = data?.mobile || data?.phone;
        if (Array.isArray(mobile)) {
            mobile = mobile[0];
        }

        if (mobile) {
            const device = await Device.findOne({ deviceNumber: mobile });
            
            if (device) {
                const payload = {
                    ts: data?.date || new Date().getTime(),
                    payload: data?.msg || data.content,
                    // from_client_id: phoneNumber,
                    from_client_id: mobile,
                }
               
                const user = await UserModel.findOne({phoneNumber:device.phoneNumber});
                console.log(user, "is user");
                const log = new LogModel;
                log.user = `${user._id}`;
                log.deviceNumber = device.deviceNumber;
                log.deviceType = `sms`;
                log.sent = 'receive';
                log.message = JSON.stringify(payload);
                log.receiveTime = new Date();
                log.responseType = "Hook";
                await log.save();
                sendMessageToChannel(mobile, payload, "sms");
            }
        }
        return res.status(200).json({ data });
    } catch (err) {
        console.log('err sms hook', err);
        return res.status(400).json(err);
    }

});
module.exports = router;