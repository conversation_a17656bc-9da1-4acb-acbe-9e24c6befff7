const express = require("express");
const router = express.Router();
const ObjectId = require('mongoose').Types.ObjectId;
const licenseModel = require('../../models/license');
const userModel = require('../../models/user');
const Generator = require('license-key-generator');
const { getEbarimt, QPay } = require("../../utils/QPayment");
const INVOICE_VS_REAL_INVOICE_ID = require('../../utils/QPayment').INVOICE_VS_REAL_INVOICE_ID;
const Order = require('../../models/order');
const { sendSms } = require("../../utils/channel");
const wallet = require("../../models/wallet");

/******************* */

router.post('/ebarimt', getEbarimt);

router.get(
    '/qpay/:page/:invoice/:cost/:sender/:phoneNumber',
    async (req, res) => {

        try {
            console.log(req.params, " is payment hook");

            const { invoice, sender, cost, page, phoneNumber } = req.params;
            if (page == 'license') {
                const days = parseFloat(cost) / parseFloat(process.env.PRICE_PER_MONTH) * 31;
                const user = await userModel.findById(sender);
                const license = new licenseModel();

                const options = {
                    type: "random",
                    length: 12,
                    group: 3,
                    split: '-',
                    splitStatus: true
                }
                const code = new Generator(options);
                console.log(INVOICE_VS_REAL_INVOICE_ID, " is invoice-vs-real-invoice");
                code.get(async (err, key) => {
                    console.log(key, " is license key")
                    if (key) {

                        let offset = 0;
                        if (user.expired)
                            offset = new Date(user.expired).getTime() - Date.now();
                        const expired = ((!user.licenseKey || user.licenseKey === "" || offset < 0) ? Date.now() : (new Date(user.expired).getTime())) + days * 3600 * 24 * 1000;

                        license.owner = ObjectId(sender);
                        license.invoice = invoice;
                        license.cost = cost;
                        license.licenseKey = key;
                        license.expired = new Date(expired);
                        license.realInvoice = INVOICE_VS_REAL_INVOICE_ID[invoice];
                        await license.save();

                        user.expired = new Date(expired);
                        user.licenseKey = key;
                        user.status = "active";

                        await user.save();

                        const sms = {
                            mobile: user.phoneNumber,
                            sms: ''
                        }
                        sms.sms = `sain bn u, tani license amjilttai sungagdlaa www.aslaa.mn`

                        const response = await sendSms(sms, {}, res);
                    }
                })
            }
            if (page == 'order') {
                const order = await Order.findOne({ phoneNumber });
                if (order) {
                    order.paid = true;
                    order.invoiceId = invoice;
                    order.realInvoiceId = INVOICE_VS_REAL_INVOICE_ID[invoice];
                    await order.save();


                    const sms = {
                        mobile: phoneNumber,
                        sms: ''
                    }
                    sms.sms = `sain bn u, tani ${order.CarModel} mashind asaaltin tohooromj suuriluulah zahialga batalgaajlaa www.aslaa.mn`

                    const response = await sendSms(sms, {}, res);
                }
            }
            if (page == 'balance') {
                const user = await userModel.findById(sender);
                user.balance = (user?.balance || 0) + parseInt(cost);
                await user.save();
                wallet.updateOne(
                    {
                        user: sender,
                    },
                    {
                        $set: { user: sender, currentBalance: user.balance, },
                        $push: { transactions: { ts: Date.now(), mode: 'deposit', description: 'QPay Hook', before: (user.balance - parseInt(cost)), amount: parseInt(cost), invoice, realInvoiceId: INVOICE_VS_REAL_INVOICE_ID[invoice] } }
                    },
                    {
                        upsert: true
                    }
                ).then(res)
            }


        } catch (err) {
            console.log(err);

        }
        res.status(200).json({ success: true });
    }
)


// This route should be accessible at /api/hook/payment/check/:invoiceId
router.get('/check/:invoiceId', async (req, res) => {
    try {
        const { invoiceId } = req.params;
        
        // First get the real invoice ID from our mapping
        const realInvoiceId = INVOICE_VS_REAL_INVOICE_ID[invoiceId];
        
        if (!realInvoiceId) {
            return res.status(404).json({
                success: false,
                message: "Invoice ID not found in our records"
            });
        }

        // Get token
        const token = await QPay().getAuthToken();
        if (!token) {
            return res.status(401).json({
                success: false,
                message: "Could not get QPay token"
            });
        }

        // Check payment using real invoice ID
        const paymentCheck = await QPay().paymentCheck(token, realInvoiceId);
        
        if (paymentCheck.status === 200) {
            return res.json({
                success: true,
                payment: paymentCheck.result,
                invoiceId: invoiceId,
                realInvoiceId: realInvoiceId
            });
        }

        return res.status(404).json({
            success: false,
            message: "Payment not found",
            invoiceId: invoiceId,
            realInvoiceId: realInvoiceId
        });

    } catch (error) {
        console.error("Manual payment check error:", error);
        return res.status(500).json({
            success: false,
            error: error.message,
            invoiceId: req.params.invoiceId
        });
    }
});


module.exports = router;
