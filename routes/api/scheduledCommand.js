const express = require('express');
const router = express.Router();
const auth = require('../../middleware/auth');
const {
  createScheduledCommand,
  getScheduledCommands,
  getScheduledCommandById,
  updateScheduledCommand,
  deleteScheduledCommand
} = require('../../controller/scheduledCommandController');

// Make sure the test route is defined BEFORE other routes to avoid parameter conflicts
// Add a test route that doesn't require auth
router.get('/test', (req, res) => {
  res.json({ message: 'Scheduled command route is working' });
});

// Create a new scheduled command
router.post('/create', auth, createScheduledCommand);

// Get all scheduled commands for the authenticated user
router.get('/', auth, getScheduledCommands);

// Get a specific scheduled command by ID
router.get('/:id', auth, getScheduledCommandById);

// Update a scheduled command
router.put('/:id', auth, updateScheduledCommand);

// Delete a scheduled command
router.delete('/:id', auth, deleteScheduledCommand);

module.exports = router;
